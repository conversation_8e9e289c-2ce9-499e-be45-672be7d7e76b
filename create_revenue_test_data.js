const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:3000'; // API Gateway port
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// Hàm đăng nhập để lấy token admin
async function loginAdmin() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
      username: ADMIN_USERNAME,
      password: ADMIN_PASSWORD
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Lỗi đăng nhập admin:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm tạo dữ liệu test doanh thu
async function createRevenueTestData() {
  try {
    console.log('🚀 Tạo dữ liệu test doanh thu...\n');
    
    // Đăng nhập admin để lấy token
    const token = await loginAdmin();
    console.log('✅ Đăng nhập admin thành công!\n');
    
    // Lấy danh sách bàn
    const tablesResponse = await axios.get(`${API_BASE_URL}/api/tables`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const tables = tablesResponse.data;
    if (tables.length === 0) {
      console.log('❌ Không có bàn nào. Vui lòng tạo bàn trước.');
      return;
    }
    
    console.log(`📋 Tìm thấy ${tables.length} bàn`);
    
    // Lấy danh sách món ăn
    const foodsResponse = await axios.get(`${API_BASE_URL}/api/menu/foods`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const foods = foodsResponse.data;
    if (foods.length === 0) {
      console.log('❌ Không có món ăn nào. Vui lòng tạo món ăn trước.');
      return;
    }
    
    console.log(`🍽️ Tìm thấy ${foods.length} món ăn`);
    
    // Tạo đơn hàng test cho 7 ngày gần đây
    const ordersCreated = [];
    
    for (let dayOffset = 6; dayOffset >= 0; dayOffset--) {
      const orderDate = new Date();
      orderDate.setDate(orderDate.getDate() - dayOffset);
      
      // Tạo 2-5 đơn hàng mỗi ngày
      const ordersPerDay = Math.floor(Math.random() * 4) + 2;
      
      console.log(`\n📅 Tạo ${ordersPerDay} đơn hàng cho ngày ${orderDate.toLocaleDateString('vi-VN')}`);
      
      for (let i = 0; i < ordersPerDay; i++) {
        try {
          // Chọn bàn ngẫu nhiên
          const randomTable = tables[Math.floor(Math.random() * tables.length)];
          
          // Chọn 1-4 món ăn ngẫu nhiên
          const itemCount = Math.floor(Math.random() * 4) + 1;
          const orderItems = [];
          
          for (let j = 0; j < itemCount; j++) {
            const randomFood = foods[Math.floor(Math.random() * foods.length)];
            const quantity = Math.floor(Math.random() * 3) + 1;
            
            orderItems.push({
              food_id: randomFood.id,
              quantity: quantity,
              price: randomFood.price
            });
          }
          
          // Tạo đơn hàng
          const orderResponse = await axios.post(`${API_BASE_URL}/api/orders`, {
            table_id: randomTable.id,
            items: orderItems,
            user_id: null
          }, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          
          const newOrder = orderResponse.data;
          
          // Cập nhật thời gian đơn hàng về ngày cụ thể
          const randomHour = Math.floor(Math.random() * 12) + 10; // 10-21h
          const randomMinute = Math.floor(Math.random() * 60);
          orderDate.setHours(randomHour, randomMinute, 0, 0);
          
          await axios.put(`${API_BASE_URL}/api/orders/${newOrder.id}`, {
            status: 'Đã thanh toán',
            order_time: orderDate.toISOString()
          }, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          
          ordersCreated.push({
            id: newOrder.id,
            table: randomTable.name,
            total: newOrder.total,
            date: orderDate.toLocaleDateString('vi-VN'),
            time: orderDate.toLocaleTimeString('vi-VN')
          });
          
          console.log(`  ✅ Đơn hàng #${newOrder.id} - Bàn ${randomTable.name} - ${newOrder.total.toLocaleString('vi-VN')}đ`);
          
        } catch (error) {
          console.error(`  ❌ Lỗi tạo đơn hàng:`, error.response?.data?.message || error.message);
        }
      }
    }
    
    console.log(`\n🎉 Đã tạo thành công ${ordersCreated.length} đơn hàng test!`);
    
    // Tính tổng doanh thu
    const totalRevenue = ordersCreated.reduce((sum, order) => sum + order.total, 0);
    console.log(`💰 Tổng doanh thu test: ${totalRevenue.toLocaleString('vi-VN')}đ`);
    
    // Hiển thị doanh thu theo ngày
    console.log('\n📊 Doanh thu theo ngày:');
    const revenueByDate = {};
    
    ordersCreated.forEach(order => {
      if (!revenueByDate[order.date]) {
        revenueByDate[order.date] = 0;
      }
      revenueByDate[order.date] += order.total;
    });
    
    Object.keys(revenueByDate).sort().forEach(date => {
      console.log(`  ${date}: ${revenueByDate[date].toLocaleString('vi-VN')}đ`);
    });
    
    console.log('\n✅ Hoàn thành tạo dữ liệu test doanh thu!');
    console.log('🔄 Hãy refresh Dashboard để xem biểu đồ doanh thu mới.');
    
  } catch (error) {
    console.error('💥 Lỗi khi tạo dữ liệu test:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Hàm xóa dữ liệu test (nếu cần)
async function cleanupTestData() {
  try {
    console.log('🧹 Xóa dữ liệu test...\n');
    
    const token = await loginAdmin();
    
    // Lấy tất cả đơn hàng
    const ordersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const orders = ordersResponse.data;
    const testOrders = orders.filter(order => 
      order.status === 'Đã thanh toán' && 
      new Date(order.order_time) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );
    
    console.log(`🗑️ Tìm thấy ${testOrders.length} đơn hàng test để xóa`);
    
    for (const order of testOrders) {
      try {
        await axios.delete(`${API_BASE_URL}/api/orders/${order.id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`  ✅ Đã xóa đơn hàng #${order.id}`);
      } catch (error) {
        console.log(`  ❌ Lỗi xóa đơn hàng #${order.id}:`, error.response?.data?.message);
      }
    }
    
    console.log('\n✅ Hoàn thành xóa dữ liệu test!');
    
  } catch (error) {
    console.error('💥 Lỗi khi xóa dữ liệu test:', error.message);
  }
}

// Chạy script
if (require.main === module) {
  const action = process.argv[2];
  
  if (action === 'cleanup') {
    cleanupTestData();
  } else {
    createRevenueTestData();
  }
}

module.exports = { createRevenueTestData, cleanupTestData };
