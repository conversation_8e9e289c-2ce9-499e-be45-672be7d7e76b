# 🔧 Hướng dẫn Debug Dashboard - Dữ liệu không chính xác

## ✅ Đã cải tiến Dashboard với:

### 🛡️ Error Handling tốt hơn:
- Hiển thị "N/A" khi API call thất bại
- Console.log chi tiết để debug
- Fallback values cho tất cả thống kê

### 🔍 Debug Console Logs:
Mở Developer Tools (F12) → Console tab để xem:
- `Tables data:` - Dữ liệu bàn thô
- `Today orders data:` - Đơn hàng hôm nay
- `Tables for empty count:` - Chi tiết trạng thái bàn
- `Storage stats:` - Thống kê lưu trữ

## 🚀 Cách kiểm tra dữ liệu:

### 1. Mở trang admin và kiểm tra Console:
```
1. Mở http://localhost:3000/admin-web/
2. <PERSON><PERSON><PERSON> nhập với tài khoản admin
3. Nhấn F12 → Console tab
4. Refresh trang và xem logs
```

### 2. Kiểm tra từng thống kê:

#### 🏠 **Bàn đang phục vụ = 0**
**Nguyên nhân có thể:**
- Không có bàn nào có status = "Đang phục vụ"
- Status có thể là "Occupied" hoặc giá trị khác

**Cách sửa:**
```javascript
// Đã thêm support cho nhiều status:
table.status === 'Đang phục vụ' || 
table.status === 'Occupied' || 
table.status === 'occupied'
```

#### 📦 **Đơn hàng hôm nay = 1**
**Kiểm tra:**
- Xem console log "Today orders data"
- Kiểm tra filter ngày có đúng không

#### 🔥 **Món ăn đang chế biến = 0**
**Nguyên nhân:**
- Kitchen service chưa có dữ liệu
- Cần tạo đơn hàng và chuyển vào nhà bếp

#### ⚠️ **Nguyên liệu sắp hết = 6**
**Bình thường** - có thể có nguyên liệu thực sự sắp hết

#### 👥 **Tổng số người dùng = 4**
**Bình thường** - có admin + 3 user khác

#### 🍽️ **Tổng số món ăn = 82**
**Tốt** - có dữ liệu món ăn

#### 🪑 **Bàn trống = 0**
**Nguyên nhân có thể:**
- Tất cả bàn đang "Đang phục vụ"
- Status bàn trống không phải "Trống"

**Cách sửa:**
```javascript
// Đã thêm support cho nhiều status trống:
table.status === 'Trống' || 
table.status === 'Available' || 
table.status === 'Empty'
```

#### 💰 **Doanh thu hôm nay = 0đ**
**Nguyên nhân:**
- Không có đơn hàng nào có status = "Đã thanh toán" hôm nay
- Cần thanh toán một số đơn hàng

## 🔧 Cách tạo dữ liệu test:

### 1. Tạo đơn hàng test:
```
1. Vào trang Quản lý bàn
2. Tạo QR code cho một bàn
3. Quét QR và đặt món
4. Vào Quản lý đơn hàng → Đổi status thành "Đã thanh toán"
```

### 2. Thay đổi trạng thái bàn:
```
1. Vào Quản lý bàn
2. Chỉnh sửa một số bàn
3. Đổi status thành "Trống" hoặc "Đang phục vụ"
```

### 3. Kiểm tra database trực tiếp:
```sql
-- Xem trạng thái bàn
SELECT name, status FROM tables;

-- Xem đơn hàng hôm nay
SELECT id, status, total, order_time 
FROM orders 
WHERE CAST(order_time AS DATE) = CAST(GETDATE() AS DATE);

-- Đếm đơn hàng theo status
SELECT status, COUNT(*) as count 
FROM orders 
GROUP BY status;
```

## 🎯 Kết quả mong đợi sau khi sửa:

### ✅ Nếu có dữ liệu:
- **Bàn đang phục vụ**: Số thực tế > 0
- **Đơn hàng hôm nay**: Số đơn hàng được tạo hôm nay
- **Bàn trống**: Số bàn có status trống
- **Doanh thu hôm nay**: Tổng tiền từ đơn đã thanh toán

### ⚠️ Nếu không có dữ liệu:
- Hiển thị "N/A" thay vì 0
- Console sẽ có error logs rõ ràng
- Không crash trang

## 🚨 Lỗi thường gặp:

### 1. **API không phản hồi**
```
Error: Failed to fetch tables: 500
→ Kiểm tra service có chạy không
```

### 2. **Token hết hạn**
```
Error: Failed to fetch: 401
→ Đăng nhập lại
```

### 3. **Database trống**
```
All values = 0
→ Cần thêm dữ liệu test
```

## 📞 Hỗ trợ debug:

Nếu vẫn có vấn đề, cung cấp:
1. Screenshot console logs
2. Screenshot trang dashboard
3. Thông tin về dữ liệu trong database
