const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:3000';
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// Hàm đăng nhập
async function loginAdmin() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
      username: ADMIN_USERNAME,
      password: ADMIN_PASSWORD
    });
    return response.data.token;
  } catch (error) {
    console.error('Lỗi đăng nhập:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm debug dữ liệu doanh thu
async function debugRevenueData() {
  try {
    console.log('🔍 DEBUG DOANH THU - Kiểm tra dữ liệu chi tiết\n');
    
    const token = await loginAdmin();
    console.log('✅ Đăng nhập thành công!\n');
    
    // 1. <PERSON>ểm tra tất cả đơn hàng
    console.log('📦 1. KIỂM TRA TẤT CẢ ĐƠN HÀNG:');
    const allOrdersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const allOrders = allOrdersResponse.data;
    console.log(`   - Tổng số đơn hàng: ${allOrders.length}`);
    
    // Phân tích theo status
    const statusCounts = {};
    allOrders.forEach(order => {
      const status = order.status || 'undefined';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    console.log('   - Phân bố theo status:');
    Object.keys(statusCounts).forEach(status => {
      console.log(`     * ${status}: ${statusCounts[status]} đơn`);
    });
    
    // 2. Kiểm tra đơn hàng đã thanh toán
    console.log('\n💰 2. KIỂM TRA ĐƠN HÀNG ĐÃ THANH TOÁN:');
    const paidOrders = allOrders.filter(order => 
      order.status === 'Đã thanh toán' || 
      order.status === 'Hoàn thành' || 
      order.status === 'Đã hoàn thành' ||
      order.payment_status === 'paid'
    );
    
    console.log(`   - Số đơn đã thanh toán: ${paidOrders.length}`);
    
    if (paidOrders.length > 0) {
      console.log('   - Chi tiết 5 đơn gần nhất:');
      paidOrders.slice(-5).forEach(order => {
        const orderDate = new Date(order.order_time);
        console.log(`     * #${order.id}: ${order.total?.toLocaleString('vi-VN')}đ - ${orderDate.toLocaleDateString('vi-VN')} ${orderDate.toLocaleTimeString('vi-VN')} - ${order.status}`);
      });
      
      const totalRevenue = paidOrders.reduce((sum, order) => sum + (parseFloat(order.total) || 0), 0);
      console.log(`   - Tổng doanh thu: ${totalRevenue.toLocaleString('vi-VN')}đ`);
    }
    
    // 3. Kiểm tra doanh thu 7 ngày gần đây
    console.log('\n📅 3. DOANH THU 7 NGÀY GẦN ĐÂY:');
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 6);
    
    const fromDate = startDate.toISOString().split('T')[0];
    const toDate = endDate.toISOString().split('T')[0];
    
    console.log(`   - Khoảng thời gian: ${fromDate} đến ${toDate}`);
    
    const recentOrdersResponse = await axios.get(`${API_BASE_URL}/api/orders?from_date=${fromDate}&to_date=${toDate}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const recentOrders = recentOrdersResponse.data;
    console.log(`   - Đơn hàng trong khoảng: ${recentOrders.length}`);
    
    const recentPaidOrders = recentOrders.filter(order => 
      order.status === 'Đã thanh toán' || 
      order.status === 'Hoàn thành' || 
      order.status === 'Đã hoàn thành' ||
      order.payment_status === 'paid'
    );
    
    console.log(`   - Đơn đã thanh toán: ${recentPaidOrders.length}`);
    
    // Nhóm theo ngày
    const revenueByDate = {};
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      revenueByDate[dateStr] = 0;
    }
    
    recentPaidOrders.forEach(order => {
      const orderDate = new Date(order.order_time).toISOString().split('T')[0];
      const orderTotal = parseFloat(order.total) || 0;
      
      if (revenueByDate.hasOwnProperty(orderDate)) {
        revenueByDate[orderDate] += orderTotal;
      }
    });
    
    console.log('   - Doanh thu theo ngày:');
    Object.keys(revenueByDate).sort().forEach(date => {
      const dateObj = new Date(date);
      const revenue = revenueByDate[date];
      console.log(`     * ${dateObj.toLocaleDateString('vi-VN')}: ${revenue.toLocaleString('vi-VN')}đ`);
    });
    
    const weekTotal = Object.values(revenueByDate).reduce((sum, val) => sum + val, 0);
    console.log(`   - Tổng doanh thu 7 ngày: ${weekTotal.toLocaleString('vi-VN')}đ`);
    
    // 4. Kiểm tra dữ liệu có vấn đề
    console.log('\n⚠️ 4. KIỂM TRA DỮ LIỆU CÓ VẤN ĐỀ:');
    
    const ordersWithoutTotal = allOrders.filter(order => !order.total || order.total <= 0);
    console.log(`   - Đơn hàng không có total hoặc total <= 0: ${ordersWithoutTotal.length}`);
    
    const ordersWithoutDate = allOrders.filter(order => !order.order_time);
    console.log(`   - Đơn hàng không có order_time: ${ordersWithoutDate.length}`);
    
    const ordersWithInvalidStatus = allOrders.filter(order => !order.status);
    console.log(`   - Đơn hàng không có status: ${ordersWithInvalidStatus.length}`);
    
    // 5. Đề xuất giải pháp
    console.log('\n💡 5. ĐỀ XUẤT GIẢI PHÁP:');
    
    if (paidOrders.length === 0) {
      console.log('   ❌ KHÔNG CÓ ĐƠN HÀNG ĐÃ THANH TOÁN');
      console.log('   🔧 Giải pháp:');
      console.log('      1. Tạo dữ liệu test: node create_revenue_test_data.js');
      console.log('      2. Hoặc thay đổi status một số đơn hàng thành "Đã thanh toán"');
      console.log('      3. Kiểm tra quy trình thanh toán trong ứng dụng');
    } else if (weekTotal === 0) {
      console.log('   ❌ KHÔNG CÓ DOANH THU 7 NGÀY GẦN ĐÂY');
      console.log('   🔧 Giải pháp:');
      console.log('      1. Tạo đơn hàng test cho 7 ngày gần đây');
      console.log('      2. Kiểm tra filter ngày tháng có đúng không');
    } else {
      console.log('   ✅ DỮ LIỆU DOANH THU TRÔNG ỔN');
      console.log('   🔧 Nếu biểu đồ vẫn không hiển thị:');
      console.log('      1. Kiểm tra Console trong browser (F12)');
      console.log('      2. Kiểm tra Chart.js đã load chưa');
      console.log('      3. Refresh trang Dashboard');
    }
    
    console.log('\n✅ Hoàn thành debug doanh thu!');
    
  } catch (error) {
    console.error('💥 Lỗi khi debug:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Chạy script
if (require.main === module) {
  debugRevenueData();
}

module.exports = { debugRevenueData };
