# 🔧 Debug Doanh thu - Hướng dẫn sửa lỗi dữ liệu

## ✅ Đã cải tiến hệ thống debug doanh thu!

### 🔍 **Các cải tiến debug:**

#### 📊 **Enhanced logging:**
- <PERSON><PERSON><PERSON> logs chi tiết với emoji
- Phân tích status distribution
- Kiểm tra dữ liệu từng bước
- Hiển thị doanh thu theo ngày

#### 🛡️ **Improved filtering:**
- Hỗ trợ nhiều status: "Đã thanh toán", "Ho<PERSON>n thành", "Đã hoàn thành"
- <PERSON><PERSON><PERSON> tra `payment_status = 'paid'`
- Validate `total > 0` và `order_time` exists
- Parse `total` thành float để tránh lỗi string

#### ⏰ **Better date handling:**
- Set thời gian chính xác (00:00:00 - 23:59:59)
- Timezone handling
- Date range validation

## 🚀 **Cách debug doanh thu:**

### 1. **Chạy script debug:**
```bash
node debug_revenue_data.js
```

**Kết quả sẽ hiển thị:**
- 📦 Tổng số đơn hàng và phân bố status
- 💰 Số đơn đã thanh toán và tổng doanh thu
- 📅 Doanh thu 7 ngày gần đây theo từng ngày
- ⚠️ Các vấn đề với dữ liệu
- 💡 Đề xuất giải pháp cụ thể

### 2. **Kiểm tra trong Browser:**
```javascript
// Mở Console (F12) và chạy:
// Sẽ thấy các logs:
🔍 Fetching revenue data from 2024-01-10 to 2024-01-16 (7 days)
📦 Raw orders data for chart: [...]
📊 Total orders fetched: 15
📈 Order status distribution: {"Đang phục vụ": 10, "Đã thanh toán": 5}
💰 Paid orders found: 5 out of 15
✅ Valid paid order: ID=1, Total=150000, Date=2024-01-15T10:30:00Z, Status=Đã thanh toán
💵 Added 150000 to 2024-01-15, new total: 150000
📊 Final processed revenue data: {...}
```

### 3. **Tạo dữ liệu test (nếu cần):**
```bash
node create_revenue_test_data.js
```

**Script sẽ:**
- Tạo 2-5 đơn hàng mỗi ngày trong 7 ngày gần đây
- Đặt status = "Đã thanh toán"
- Tính tổng doanh thu và hiển thị theo ngày
- Sử dụng dữ liệu bàn và món ăn có sẵn

## 🔍 **Các vấn đề thường gặp:**

### ❌ **1. Không có đơn hàng đã thanh toán:**
```
📈 Order status distribution: {"Đang phục vụ": 10, "Chờ xác nhận": 5}
💰 Paid orders found: 0 out of 15
```

**Giải pháp:**
```sql
-- Cập nhật status một số đơn hàng:
UPDATE orders SET status = N'Đã thanh toán' WHERE id IN (1,2,3,4,5);
```

### ❌ **2. Đơn hàng có total = 0 hoặc null:**
```
⚠️ Order ID=1 has invalid total: null
```

**Giải pháp:**
```sql
-- Cập nhật total cho đơn hàng:
UPDATE orders SET total = 150000 WHERE id = 1;
```

### ❌ **3. Đơn hàng không có order_time:**
```
⚠️ Order ID=2 has no order_time
```

**Giải pháp:**
```sql
-- Cập nhật order_time:
UPDATE orders SET order_time = GETDATE() WHERE order_time IS NULL;
```

### ❌ **4. Dữ liệu ngoài khoảng thời gian:**
```
⚠️ Order date 2024-01-01 is outside the period range
```

**Giải pháp:**
- Đơn hàng quá cũ, không ảnh hưởng đến biểu đồ 7 ngày gần đây
- Hoặc tạo đơn hàng mới trong khoảng thời gian

## 🎯 **Kiểm tra từng bước:**

### **Bước 1: Kiểm tra API response**
```javascript
// Trong Console browser:
fetch('/api/orders?from_date=2024-01-10&to_date=2024-01-16', {
  headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
})
.then(r => r.json())
.then(data => console.log('API Response:', data));
```

### **Bước 2: Kiểm tra filter logic**
```javascript
// Kiểm tra đơn hàng đã thanh toán:
const paidOrders = orders.filter(order => 
  order.status === 'Đã thanh toán' || 
  order.status === 'Hoàn thành' || 
  order.payment_status === 'paid'
);
console.log('Paid orders:', paidOrders);
```

### **Bước 3: Kiểm tra date grouping**
```javascript
// Kiểm tra nhóm theo ngày:
const revenueByDate = {};
paidOrders.forEach(order => {
  const date = new Date(order.order_time).toISOString().split('T')[0];
  revenueByDate[date] = (revenueByDate[date] || 0) + parseFloat(order.total);
});
console.log('Revenue by date:', revenueByDate);
```

## 🛠️ **Tools hỗ trợ:**

### **1. Debug script:**
- `debug_revenue_data.js` - Phân tích toàn diện
- Hiển thị chi tiết từng bước
- Đề xuất giải pháp cụ thể

### **2. Test data script:**
- `create_revenue_test_data.js` - Tạo dữ liệu test
- `node create_revenue_test_data.js cleanup` - Xóa dữ liệu test

### **3. Browser Console:**
- F12 → Console tab
- Xem real-time logs khi load biểu đồ
- Debug từng function riêng lẻ

## 📊 **Kết quả mong đợi:**

### ✅ **Khi dữ liệu đúng:**
```
🔍 Fetching revenue data from 2024-01-10 to 2024-01-16 (7 days)
📊 Total orders fetched: 25
💰 Paid orders found: 12 out of 25
📊 Final processed revenue data: {
  totalRevenue: 1850000,
  labels: ["10 Th1", "11 Th1", "12 Th1", ...],
  data: [250000, 0, 300000, 150000, 400000, 350000, 400000]
}
```

### ✅ **Biểu đồ hiển thị:**
- Line/Bar chart với dữ liệu chính xác
- Tooltip hiển thị đúng số tiền
- Summary cards cập nhật đúng
- Không có error trong Console

## 🚨 **Lưu ý quan trọng:**

### **1. Database schema:**
- Đảm bảo trường `total` là DECIMAL(18,2)
- Trường `order_time` là DATETIME
- Trường `status` là NVARCHAR

### **2. API consistency:**
- Tất cả API trả về cùng format
- Status values nhất quán
- Date format chuẩn ISO

### **3. Frontend handling:**
- Parse `total` thành number
- Handle null/undefined values
- Proper date parsing

## ✅ **Checklist debug:**

- [ ] Chạy `node debug_revenue_data.js`
- [ ] Kiểm tra có đơn hàng "Đã thanh toán" không
- [ ] Kiểm tra `total > 0` và `order_time` exists
- [ ] Xem Console logs trong browser
- [ ] Kiểm tra Chart.js đã load chưa
- [ ] Refresh Dashboard và xem biểu đồ

**Sau khi debug xong, biểu đồ doanh thu sẽ hiển thị chính xác!** 📈✨
