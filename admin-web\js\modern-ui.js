/* ==================== MODERN UI CONTROLLER ==================== */

class ModernUIController {
    constructor() {
        this.sidebar = document.getElementById('modern-sidebar');
        this.mainContent = document.getElementById('main-content');
        this.sidebarOverlay = document.getElementById('sidebar-overlay');
        this.sidebarToggle = document.getElementById('sidebar-toggle');
        this.pageTitle = document.getElementById('page-title');
        
        this.isMobile = window.innerWidth <= 1024;
        this.sidebarCollapsed = localStorage.getItem('modern-sidebar-collapsed') === 'true';
        this.currentPage = 'dashboard';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupNavigation();
        this.setupAnimations();
        this.handleResize();
        this.updatePageTitle();
        this.restoreState();
        this.initializeCharts();
    }

    setupEventListeners() {
        // Sidebar toggle
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Sidebar overlay click (mobile)
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Escape key to close mobile sidebar
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMobile) {
                this.closeMobileSidebar();
            }
        });

        // Smooth scrolling for internal links
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href^="#"]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item[data-page]');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.navigateToPage(page);
                
                // Close mobile sidebar after navigation
                if (this.isMobile) {
                    this.closeMobileSidebar();
                }
            });
        });
    }

    setupAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-slide-in-up');
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        const animatableElements = document.querySelectorAll('.stats-card, .card-modern');
        animatableElements.forEach(element => {
            observer.observe(element);
        });

        // Add stagger animation delays
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
            return;
        }

        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            this.sidebar.classList.add('collapsed');
            this.mainContent.classList.add('expanded');
        } else {
            this.sidebar.classList.remove('collapsed');
            this.mainContent.classList.remove('expanded');
        }

        localStorage.setItem('modern-sidebar-collapsed', this.sidebarCollapsed);
    }

    toggleMobileSidebar() {
        const isOpen = this.sidebar.classList.contains('mobile-open');
        
        if (isOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    openMobileSidebar() {
        this.sidebar.classList.remove('mobile-hidden');
        this.sidebar.classList.add('mobile-open');
        this.sidebarOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeMobileSidebar() {
        this.sidebar.classList.remove('mobile-open');
        this.sidebar.classList.add('mobile-hidden');
        this.sidebarOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    navigateToPage(page) {
        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(p => p.classList.remove('active'));

        // Show target page
        const targetPage = document.getElementById(page);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update navigation active state
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));
        
        const activeNavItem = document.querySelector(`.nav-item[data-page="${page}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Update page title
        this.currentPage = page;
        this.updatePageTitle();

        // Save current page
        localStorage.setItem('modern-current-page', page);

        // Trigger page change event
        this.onPageChange(page);
    }

    updatePageTitle() {
        const pageTitles = {
            'dashboard': 'Dashboard',
            'analytics': 'Phân tích',
            'tables': 'Quản lý bàn',
            'menu': 'Thực đơn',
            'orders': 'Đơn hàng',
            'kitchen': 'Bếp',
            'inventory': 'Kho hàng',
            'users': 'Người dùng',
            'settings': 'Cài đặt'
        };

        const title = pageTitles[this.currentPage] || 'Dashboard';
        if (this.pageTitle) {
            this.pageTitle.textContent = title;
        }
        document.title = `${title} - Restaurant Pro`;
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 1024;

        // If switching from mobile to desktop
        if (wasMobile && !this.isMobile) {
            this.closeMobileSidebar();
            
            // Restore desktop sidebar state
            if (this.sidebarCollapsed) {
                this.sidebar.classList.add('collapsed');
                this.mainContent.classList.add('expanded');
            }
        }

        // If switching from desktop to mobile
        if (!wasMobile && this.isMobile) {
            this.sidebar.classList.remove('collapsed');
            this.mainContent.classList.remove('expanded');
            this.sidebar.classList.add('mobile-hidden');
        }
    }

    restoreState() {
        // Restore sidebar state
        if (this.sidebarCollapsed && !this.isMobile) {
            this.sidebar.classList.add('collapsed');
            this.mainContent.classList.add('expanded');
        }

        if (this.isMobile) {
            this.sidebar.classList.add('mobile-hidden');
        }

        // Restore current page
        const savedPage = localStorage.getItem('modern-current-page');
        if (savedPage) {
            this.navigateToPage(savedPage);
        }
    }

    initializeCharts() {
        // Initialize revenue chart
        this.initRevenueChart();
        
        // Initialize orders chart
        this.initOrdersChart();
        
        // Setup chart filter buttons
        this.setupChartFilters();
    }

    initRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        this.revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
                datasets: [{
                    label: 'Doanh thu',
                    data: [1200000, 1900000, 3000000, 2500000, 2200000, 3200000, 2800000],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(59, 130, 246)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('vi-VN', {
                                    style: 'currency',
                                    currency: 'VND'
                                }).format(value);
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    initOrdersChart() {
        const ctx = document.getElementById('ordersChart');
        if (!ctx) return;

        this.ordersChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Hoàn thành', 'Đang chế biến', 'Đang phục vụ', 'Đã hủy'],
                datasets: [{
                    data: [65, 20, 12, 3],
                    backgroundColor: [
                        'rgb(34, 197, 94)',
                        'rgb(59, 130, 246)',
                        'rgb(245, 158, 11)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 0,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    }
                }
            }
        });
    }

    setupChartFilters() {
        const filterButtons = document.querySelectorAll('.chart-filter-btn');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-secondary');
                    btn.classList.remove('active');
                });
                
                // Add active class to clicked button
                button.classList.add('btn-primary');
                button.classList.remove('btn-secondary');
                button.classList.add('active');
                
                // Update chart data based on period
                const period = button.dataset.period;
                this.updateChartData(period);
            });
        });
    }

    updateChartData(period) {
        // Simulate different data for different periods
        const data = {
            '7': {
                labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
                values: [1200000, 1900000, 3000000, 2500000, 2200000, 3200000, 2800000]
            },
            '30': {
                labels: ['Tuần 1', 'Tuần 2', 'Tuần 3', 'Tuần 4'],
                values: [15000000, 18000000, 22000000, 19000000]
            },
            '90': {
                labels: ['Tháng 1', 'Tháng 2', 'Tháng 3'],
                values: [65000000, 72000000, 68000000]
            }
        };

        if (this.revenueChart && data[period]) {
            this.revenueChart.data.labels = data[period].labels;
            this.revenueChart.data.datasets[0].data = data[period].values;
            this.revenueChart.update('active');
        }
    }

    onPageChange(page) {
        console.log(`Navigated to modern page: ${page}`);
        
        // Trigger custom event
        const event = new CustomEvent('modernPageChanged', {
            detail: { page }
        });
        document.dispatchEvent(event);
    }

    // Public API
    getCurrentPage() {
        return this.currentPage;
    }

    goToPage(page) {
        this.navigateToPage(page);
    }
}

// Modern Notification System
class ModernNotificationSystem {
    constructor() {
        this.container = this.createContainer();
        this.notifications = [];
    }

    createContainer() {
        const container = document.createElement('div');
        container.className = 'modern-notification-container';
        container.style.cssText = `
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            z-index: var(--z-toast);
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = 5000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        });

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `modern-notification modern-notification-${type}`;
        
        const colors = {
            success: 'var(--success-600)',
            error: 'var(--error-600)',
            warning: 'var(--warning-600)',
            info: 'var(--primary-600)'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.style.cssText = `
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-4);
            box-shadow: var(--shadow-xl);
            border-left: 4px solid ${colors[type]};
            transform: translateX(100%);
            opacity: 0;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: var(--space-3);
            pointer-events: auto;
            max-width: 100%;
        `;

        notification.innerHTML = `
            <i class="${icons[type]}" style="color: ${colors[type]}; font-size: var(--text-lg); flex-shrink: 0;"></i>
            <span style="flex: 1; color: var(--gray-900); font-weight: var(--font-medium); font-size: var(--text-sm);">${message}</span>
            <button class="modern-notification-close" style="
                background: none;
                border: none;
                color: var(--gray-400);
                cursor: pointer;
                padding: var(--space-1);
                border-radius: var(--radius-base);
                transition: color var(--transition-fast);
                flex-shrink: 0;
            ">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Close button
        const closeBtn = notification.querySelector('.modern-notification-close');
        closeBtn.addEventListener('click', () => {
            this.remove(notification);
        });

        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.color = 'var(--gray-600)';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.color = 'var(--gray-400)';
        });

        return notification;
    }

    remove(notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Modern UI Controller
    window.modernUI = new ModernUIController();
    
    // Initialize Modern Notification System
    window.modernNotifications = new ModernNotificationSystem();
    
    // Welcome notification
    setTimeout(() => {
        window.modernNotifications.success('Chào mừng đến với Restaurant Pro - Hệ thống quản lý nhà hàng hiện đại!', 4000);
    }, 1000);
});

// Export for use in other scripts
window.ModernUIController = ModernUIController;
window.ModernNotificationSystem = ModernNotificationSystem;
