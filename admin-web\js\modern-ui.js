/* ==================== MODERN UI CONTROLLER ==================== */

class ModernUIController {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.mainContent = document.getElementById('main-content');
        this.sidebarOverlay = document.getElementById('sidebar-overlay');
        this.sidebarToggle = document.getElementById('sidebar-toggle');
        this.mobileToggle = document.getElementById('mobile-toggle');
        this.pageTitle = document.getElementById('page-title');
        
        this.isMobile = window.innerWidth <= 768;
        this.sidebarCollapsed = false;
        this.currentPage = 'dashboard';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupNavigation();
        this.handleResize();
        this.updatePageTitle();
    }

    setupEventListeners() {
        // Sidebar toggle for desktop
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Mobile toggle
        if (this.mobileToggle) {
            this.mobileToggle.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }

        // Sidebar overlay click
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Escape key to close mobile sidebar
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMobile) {
                this.closeMobileSidebar();
            }
        });
    }

    setupNavigation() {
        // Navigation items
        const navItems = document.querySelectorAll('.nav-item[data-page]');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.navigateToPage(page);
                
                // Close mobile sidebar after navigation
                if (this.isMobile) {
                    this.closeMobileSidebar();
                }
            });
        });
    }

    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
            return;
        }

        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            this.sidebar.classList.add('collapsed');
            this.mainContent.classList.add('expanded');
        } else {
            this.sidebar.classList.remove('collapsed');
            this.mainContent.classList.remove('expanded');
        }

        // Save state to localStorage
        localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
    }

    toggleMobileSidebar() {
        const isOpen = this.sidebar.classList.contains('mobile-open');
        
        if (isOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    openMobileSidebar() {
        this.sidebar.classList.add('mobile-open');
        this.sidebarOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeMobileSidebar() {
        this.sidebar.classList.remove('mobile-open');
        this.sidebarOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    navigateToPage(page) {
        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(p => p.classList.remove('active'));

        // Show target page
        const targetPage = document.getElementById(page);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update navigation active state
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));
        
        const activeNavItem = document.querySelector(`.nav-item[data-page="${page}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Update page title
        this.currentPage = page;
        this.updatePageTitle();

        // Save current page to localStorage
        localStorage.setItem('currentPage', page);

        // Trigger page load event
        this.onPageChange(page);
    }

    updatePageTitle() {
        const pageTitles = {
            'dashboard': 'Dashboard',
            'tables': 'Quản lý bàn',
            'menu': 'Thực đơn',
            'orders': 'Đơn hàng',
            'kitchen': 'Nhà bếp',
            'inventory': 'Kho hàng',
            'users': 'Người dùng'
        };

        const title = pageTitles[this.currentPage] || 'Dashboard';
        if (this.pageTitle) {
            this.pageTitle.textContent = title;
        }
        document.title = `${title} - Restaurant Admin`;
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        // If switching from mobile to desktop
        if (wasMobile && !this.isMobile) {
            this.closeMobileSidebar();
            
            // Restore desktop sidebar state
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true') {
                this.sidebarCollapsed = true;
                this.sidebar.classList.add('collapsed');
                this.mainContent.classList.add('expanded');
            }
        }

        // If switching from desktop to mobile
        if (!wasMobile && this.isMobile) {
            this.sidebar.classList.remove('collapsed');
            this.mainContent.classList.remove('expanded');
            this.sidebarCollapsed = false;
        }
    }

    onPageChange(page) {
        // Override this method to handle page-specific logic
        console.log(`Navigated to page: ${page}`);
        
        // Trigger custom event
        const event = new CustomEvent('pageChanged', {
            detail: { page }
        });
        document.dispatchEvent(event);
    }

    // Public methods for external use
    getCurrentPage() {
        return this.currentPage;
    }

    goToPage(page) {
        this.navigateToPage(page);
    }

    // Restore state from localStorage
    restoreState() {
        // Restore sidebar state
        const savedSidebarState = localStorage.getItem('sidebarCollapsed');
        if (savedSidebarState === 'true' && !this.isMobile) {
            this.sidebarCollapsed = true;
            this.sidebar.classList.add('collapsed');
            this.mainContent.classList.add('expanded');
        }

        // Restore current page
        const savedPage = localStorage.getItem('currentPage');
        if (savedPage) {
            this.navigateToPage(savedPage);
        }
    }
}

// Notification System
class NotificationSystem {
    constructor() {
        this.container = this.createContainer();
        this.notifications = [];
    }

    createContainer() {
        const container = document.createElement('div');
        container.className = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: ${getComputedStyle(document.documentElement).getPropertyValue('--z-toast')};
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 400px;
        `;
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = 5000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        });

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const colors = {
            success: 'var(--success-600)',
            error: 'var(--error-600)',
            warning: 'var(--warning-600)',
            info: 'var(--primary-600)'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.style.cssText = `
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            box-shadow: var(--shadow-lg);
            border-left: 4px solid ${colors[type]};
            transform: translateX(100%);
            opacity: 0;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        `;

        notification.innerHTML = `
            <i class="${icons[type]}" style="color: ${colors[type]}; font-size: var(--font-size-lg);"></i>
            <span style="flex: 1; color: var(--gray-900); font-weight: var(--font-weight-medium);">${message}</span>
            <button class="notification-close" style="
                background: none;
                border: none;
                color: var(--gray-400);
                cursor: pointer;
                padding: var(--space-1);
                border-radius: var(--radius-base);
                transition: color var(--transition-fast);
            ">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.remove(notification);
        });

        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.color = 'var(--gray-600)';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.color = 'var(--gray-400)';
        });

        return notification;
    }

    remove(notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize UI Controller
    window.modernUI = new ModernUIController();
    
    // Initialize Notification System
    window.notifications = new NotificationSystem();
    
    // Restore state
    window.modernUI.restoreState();
    
    // Welcome notification
    setTimeout(() => {
        window.notifications.success('Chào mừng đến với hệ thống quản lý nhà hàng!', 3000);
    }, 1000);
});

// Export for use in other scripts
window.ModernUIController = ModernUIController;
window.NotificationSystem = NotificationSystem;
