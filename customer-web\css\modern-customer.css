/* ==================== MODERN CUSTOMER INTERFACE ==================== */

/* Import Design System */
@import url('../admin-web/css/design-system.css');

/* Customer Layout */
.customer-app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  font-family: var(--font-family-sans);
}

/* Header */
.customer-header {
  background: white;
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.restaurant-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xl);
}

.restaurant-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}

.table-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: var(--primary-50);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

.table-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-700);
}

/* Navigation Tabs */
.nav-tabs {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  overflow-x: auto;
}

.nav-tabs-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  padding: 0 var(--space-6);
}

.nav-tab {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  text-decoration: none;
  border-bottom: 3px solid transparent;
  transition: all var(--transition-fast);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.nav-tab:hover {
  color: var(--primary-600);
  background-color: var(--primary-50);
}

.nav-tab.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
  background-color: var(--primary-50);
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Menu Grid */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

/* Food Card */
.food-card {
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-base);
  position: relative;
}

.food-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.food-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background: var(--gray-100);
}

.food-content {
  padding: var(--space-5);
}

.food-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
  line-height: var(--line-height-tight);
}

.food-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.food-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
}

.food-price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-600);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
  font-weight: var(--font-weight-semibold);
}

.quantity-btn:hover {
  background: var(--primary-100);
  color: var(--primary-600);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  min-width: 40px;
  text-align: center;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

/* Cart */
.cart-summary {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-sticky);
  transform: translateY(100%);
  transition: transform var(--transition-base);
}

.cart-summary.visible {
  transform: translateY(0);
}

.cart-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

.cart-info {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.cart-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-600);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  position: relative;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error-500);
  color: white;
  border-radius: var(--radius-full);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.cart-details {
  display: flex;
  flex-direction: column;
}

.cart-items-count {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
}

.cart-total {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}

.checkout-btn {
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.checkout-btn:hover {
  background: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Category Filter */
.category-filter {
  display: flex;
  gap: var(--space-3);
  overflow-x: auto;
  padding: var(--space-4) 0;
  margin-bottom: var(--space-6);
}

.category-btn {
  padding: var(--space-3) var(--space-5);
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.category-btn:hover {
  border-color: var(--primary-300);
  color: var(--primary-600);
}

.category-btn.active {
  background: var(--primary-600);
  border-color: var(--primary-600);
  color: white;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.food-card-skeleton {
  height: 320px;
  border-radius: var(--radius-2xl);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: var(--space-3) var(--space-4);
  }
  
  .restaurant-name {
    font-size: var(--font-size-lg);
  }
  
  .main-content {
    padding: var(--space-4);
  }
  
  .menu-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .nav-tabs-content {
    padding: 0 var(--space-4);
  }
  
  .cart-content {
    padding: var(--space-3) var(--space-4);
  }
  
  .category-filter {
    padding: var(--space-3) 0;
  }
}

@media (max-width: 480px) {
  .food-footer {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .quantity-controls {
    justify-content: center;
  }
  
  .cart-content {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .checkout-btn {
    width: 100%;
    justify-content: center;
  }
}
