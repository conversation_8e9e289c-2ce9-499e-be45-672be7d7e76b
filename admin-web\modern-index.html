<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý <PERSON> hàng - Modern Dashboard</title>
    
    <!-- Modern Design System -->
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/modern-admin.css">
    
    <!-- External dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Overlay for Mobile -->
        <div id="sidebar-overlay" class="sidebar-overlay"></div>

        <!-- Modern Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <h2 class="sidebar-title">Restaurant Admin</h2>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Tổng quan</div>
                    <a href="#" class="nav-item active" data-page="dashboard">
                        <div class="nav-item-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <span class="nav-item-text">Dashboard</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Quản lý</div>
                    <a href="#" class="nav-item" data-page="tables">
                        <div class="nav-item-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <span class="nav-item-text">Quản lý bàn</span>
                    </a>
                    <a href="#" class="nav-item" data-page="menu">
                        <div class="nav-item-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <span class="nav-item-text">Thực đơn</span>
                    </a>
                    <a href="#" class="nav-item" data-page="orders">
                        <div class="nav-item-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <span class="nav-item-text">Đơn hàng</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Vận hành</div>
                    <a href="#" class="nav-item" data-page="kitchen">
                        <div class="nav-item-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <span class="nav-item-text">Nhà bếp</span>
                    </a>
                    <a href="#" class="nav-item" data-page="inventory">
                        <div class="nav-item-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <span class="nav-item-text">Kho hàng</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Hệ thống</div>
                    <a href="#" class="nav-item" data-page="users">
                        <div class="nav-item-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-item-text">Người dùng</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button id="sidebar-toggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button id="mobile-toggle" class="mobile-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="page-title">Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="user-menu" id="user-menu">
                        <div class="user-avatar">
                            <span class="user-name">A</span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Admin</div>
                            <div class="user-role">Quản trị viên</div>
                        </div>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                    <button id="logout-btn" class="btn btn-ghost">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="hidden sm:inline">Đăng xuất</span>
                    </button>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content">
                <!-- Dashboard Page -->
                <div id="dashboard" class="page active">
                    <div class="page-header text-center">
                        <h1>Tổng quan hệ thống</h1>
                        <p>Theo dõi hoạt động nhà hàng trong thời gian thực với analytics chi tiết</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon primary">
                                    <i class="fas fa-table"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Bàn đang phục vụ</div>
                                    <div class="stat-value" id="active-tables">0</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-trending-up"></i>
                                        <span>+12% hôm qua</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon success">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Đơn hàng hôm nay</div>
                                    <div class="stat-value" id="today-orders">0</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-trending-up"></i>
                                        <span>+8% hôm qua</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon warning">
                                    <i class="fas fa-fire-burner"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Đang chế biến</div>
                                    <div class="stat-value" id="cooking-items">0</div>
                                    <div class="stat-change neutral">
                                        <i class="fas fa-clock"></i>
                                        <span>Thời gian thực</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon error">
                                    <i class="fas fa-chart-line-down"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Doanh thu hôm nay</div>
                                    <div class="stat-value" id="today-revenue">0đ</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-trending-up"></i>
                                        <span>+15% hôm qua</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Revenue Chart Section -->
                    <div class="card mb-8" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.8);">
                        <div class="card-header" style="background: linear-gradient(135deg, var(--primary-50), var(--primary-100)); border-bottom: 1px solid var(--primary-200);">
                            <h3 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                                Phân tích doanh thu
                            </h3>
                            <p class="text-gray-600 mt-2">Theo dõi xu hướng và hiệu suất kinh doanh</p>
                        </div>
                        <div class="card-body">
                            <!-- Chart Controls -->
                            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-8">
                                <div class="flex gap-3">
                                    <button class="btn btn-primary chart-filter-btn active" data-period="7" style="border-radius: 12px; padding: 12px 24px; font-weight: 600;">
                                        <i class="fas fa-calendar-week mr-2"></i>7 ngày
                                    </button>
                                    <button class="btn btn-secondary chart-filter-btn" data-period="30" style="border-radius: 12px; padding: 12px 24px; font-weight: 600;">
                                        <i class="fas fa-calendar-alt mr-2"></i>30 ngày
                                    </button>
                                    <button class="btn btn-secondary chart-filter-btn" data-period="90" style="border-radius: 12px; padding: 12px 24px; font-weight: 600;">
                                        <i class="fas fa-calendar mr-2"></i>3 tháng
                                    </button>
                                </div>
                                <div class="flex gap-3">
                                    <button class="btn btn-success chart-type-btn active" data-type="line" style="border-radius: 12px; padding: 12px 20px;">
                                        <i class="fas fa-chart-line"></i>
                                        <span class="hidden sm:inline ml-2">Đường</span>
                                    </button>
                                    <button class="btn btn-success chart-type-btn" data-type="bar" style="border-radius: 12px; padding: 12px 20px;">
                                        <i class="fas fa-chart-bar"></i>
                                        <span class="hidden sm:inline ml-2">Cột</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Chart Container -->
                            <div class="relative mb-8" style="background: linear-gradient(135deg, #f8fafc, #f1f5f9); border-radius: 20px; padding: 24px; box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);">
                                <div style="height: 400px; position: relative;">
                                    <canvas id="revenueChart"></canvas>
                                </div>
                            </div>

                            <!-- Chart Summary -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-6 border border-primary-200">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-coins text-white text-sm"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-primary-700 uppercase tracking-wide">Tổng doanh thu</div>
                                    </div>
                                    <div class="text-3xl font-extrabold text-primary-900" id="total-revenue-period">0đ</div>
                                </div>
                                <div class="bg-gradient-to-br from-success-50 to-success-100 rounded-2xl p-6 border border-success-200">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-success-500 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-chart-bar text-white text-sm"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-success-700 uppercase tracking-wide">Trung bình/ngày</div>
                                    </div>
                                    <div class="text-3xl font-extrabold text-success-900" id="avg-revenue-day">0đ</div>
                                </div>
                                <div class="bg-gradient-to-br from-warning-50 to-warning-100 rounded-2xl p-6 border border-warning-200">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-warning-500 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-trophy text-white text-sm"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-warning-700 uppercase tracking-wide">Ngày cao nhất</div>
                                    </div>
                                    <div class="text-3xl font-extrabold text-warning-900" id="highest-revenue-day">0đ</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="enhanced-card">
                        <div class="card-header" style="background: linear-gradient(135deg, var(--success-50), var(--success-100)); border-bottom: 1px solid var(--success-200);">
                            <h3 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-success-500 to-success-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-clock-rotate-left text-white"></i>
                                </div>
                                Đơn hàng gần đây
                            </h3>
                            <p class="text-gray-600 mt-2">Theo dõi các đơn hàng mới nhất trong hệ thống</p>
                        </div>
                        <div class="card-body p-0">
                            <div class="overflow-x-auto">
                                <table class="modern-table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-hashtag text-xs"></i>
                                                    Mã đơn
                                                </div>
                                            </th>
                                            <th>
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-table text-xs"></i>
                                                    Bàn
                                                </div>
                                            </th>
                                            <th>
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-clock text-xs"></i>
                                                    Thời gian
                                                </div>
                                            </th>
                                            <th>
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-money-bill text-xs"></i>
                                                    Tổng tiền
                                                </div>
                                            </th>
                                            <th>
                                                <div class="flex items-center gap-2">
                                                    <i class="fas fa-info-circle text-xs"></i>
                                                    Trạng thái
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-orders-table">
                                        <!-- Sample data for demo -->
                                        <tr>
                                            <td class="font-semibold text-primary-600">#001</td>
                                            <td>Bàn 5</td>
                                            <td class="text-gray-500">10:30 AM</td>
                                            <td class="font-bold text-success-600">250,000đ</td>
                                            <td><span class="status-badge success">Đã thanh toán</span></td>
                                        </tr>
                                        <tr>
                                            <td class="font-semibold text-primary-600">#002</td>
                                            <td>Bàn 3</td>
                                            <td class="text-gray-500">11:15 AM</td>
                                            <td class="font-bold text-success-600">180,000đ</td>
                                            <td><span class="status-badge warning">Đang phục vụ</span></td>
                                        </tr>
                                        <tr>
                                            <td class="font-semibold text-primary-600">#003</td>
                                            <td>Bàn 7</td>
                                            <td class="text-gray-500">11:45 AM</td>
                                            <td class="font-bold text-success-600">320,000đ</td>
                                            <td><span class="status-badge info">Đang chế biến</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other pages will be added here -->
                <div id="tables" class="page">
                    <h2>Quản lý bàn - Coming Soon</h2>
                </div>

                <div id="menu" class="page">
                    <h2>Thực đơn - Coming Soon</h2>
                </div>

                <div id="orders" class="page">
                    <h2>Đơn hàng - Coming Soon</h2>
                </div>

                <div id="kitchen" class="page">
                    <h2>Nhà bếp - Coming Soon</h2>
                </div>

                <div id="inventory" class="page">
                    <h2>Kho hàng - Coming Soon</h2>
                </div>

                <div id="users" class="page">
                    <h2>Người dùng - Coming Soon</h2>
                </div>
            </div>
        </main>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Đăng nhập hệ thống</h3>
            </div>
            <form id="login-form" class="modal-body">
                <div class="form-group">
                    <label for="username" class="form-label">Tên đăng nhập</label>
                    <input type="text" id="username" class="form-input" required>
                </div>
                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <input type="password" id="password" class="form-input" required>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        Đăng nhập
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/modern-ui.js"></script>
    <script src="js/main.js"></script>

    <script>
        // Integration with existing logic
        document.addEventListener('pageChanged', (e) => {
            const page = e.detail.page;
            console.log(`Page changed to: ${page}`);

            // Load page-specific data
            if (page === 'dashboard') {
                loadDashboardData();
            }
            // Add other page handlers here
        });

        // Override notification manager to use modern notifications
        if (window.notifications) {
            window.notificationManager = {
                success: (msg) => window.notifications.success(msg),
                error: (msg) => window.notifications.error(msg),
                warning: (msg) => window.notifications.warning(msg),
                info: (msg) => window.notifications.info(msg)
            };
        }
    </script>
</body>
</html>
