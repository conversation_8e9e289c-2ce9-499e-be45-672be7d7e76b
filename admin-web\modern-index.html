<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý <PERSON> hàng - Modern Dashboard</title>
    
    <!-- Modern Design System -->
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/modern-admin.css">
    
    <!-- External dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Overlay for Mobile -->
        <div id="sidebar-overlay" class="sidebar-overlay"></div>

        <!-- Modern Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <h2 class="sidebar-title">Restaurant Admin</h2>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Tổng quan</div>
                    <a href="#" class="nav-item active" data-page="dashboard">
                        <div class="nav-item-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <span class="nav-item-text">Dashboard</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Quản lý</div>
                    <a href="#" class="nav-item" data-page="tables">
                        <div class="nav-item-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <span class="nav-item-text">Quản lý bàn</span>
                    </a>
                    <a href="#" class="nav-item" data-page="menu">
                        <div class="nav-item-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <span class="nav-item-text">Thực đơn</span>
                    </a>
                    <a href="#" class="nav-item" data-page="orders">
                        <div class="nav-item-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <span class="nav-item-text">Đơn hàng</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Vận hành</div>
                    <a href="#" class="nav-item" data-page="kitchen">
                        <div class="nav-item-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <span class="nav-item-text">Nhà bếp</span>
                    </a>
                    <a href="#" class="nav-item" data-page="inventory">
                        <div class="nav-item-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <span class="nav-item-text">Kho hàng</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Hệ thống</div>
                    <a href="#" class="nav-item" data-page="users">
                        <div class="nav-item-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-item-text">Người dùng</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button id="sidebar-toggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button id="mobile-toggle" class="mobile-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="page-title">Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="user-menu" id="user-menu">
                        <div class="user-avatar">
                            <span class="user-name">A</span>
                        </div>
                        <div class="user-info">
                            <div class="user-name">Admin</div>
                            <div class="user-role">Quản trị viên</div>
                        </div>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                    <button id="logout-btn" class="btn btn-ghost">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="hidden sm:inline">Đăng xuất</span>
                    </button>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content">
                <!-- Dashboard Page -->
                <div id="dashboard" class="page active">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Tổng quan hệ thống</h2>
                        <p class="text-gray-600">Theo dõi hoạt động nhà hàng trong thời gian thực</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="stats-container">
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon primary">
                                    <i class="fas fa-table"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Bàn đang phục vụ</div>
                                    <div class="stat-value" id="active-tables">0</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+12% so với hôm qua</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon success">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Đơn hàng hôm nay</div>
                                    <div class="stat-value" id="today-orders">0</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+8% so với hôm qua</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon warning">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Món đang chế biến</div>
                                    <div class="stat-value" id="cooking-items">0</div>
                                    <div class="stat-change">
                                        <i class="fas fa-clock"></i>
                                        <span>Thời gian thực</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon error">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">Nguyên liệu sắp hết</div>
                                    <div class="stat-value" id="low-inventory">0</div>
                                    <div class="stat-change negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>Cần bổ sung</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Revenue Chart Section -->
                    <div class="card mb-8">
                        <div class="card-header">
                            <h3 class="text-xl font-semibold text-gray-900 flex items-center gap-2">
                                <i class="fas fa-chart-line text-primary-600"></i>
                                Biểu đồ doanh thu
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Chart Controls -->
                            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                                <div class="flex gap-2">
                                    <button class="btn btn-sm btn-primary chart-filter-btn active" data-period="7">7 ngày</button>
                                    <button class="btn btn-sm btn-secondary chart-filter-btn" data-period="30">30 ngày</button>
                                    <button class="btn btn-sm btn-secondary chart-filter-btn" data-period="90">3 tháng</button>
                                </div>
                                <div class="flex gap-2">
                                    <button class="btn btn-sm btn-success chart-type-btn active" data-type="line">
                                        <i class="fas fa-chart-line"></i>
                                        <span class="hidden sm:inline">Đường</span>
                                    </button>
                                    <button class="btn btn-sm btn-success chart-type-btn" data-type="bar">
                                        <i class="fas fa-chart-bar"></i>
                                        <span class="hidden sm:inline">Cột</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Chart Container -->
                            <div class="bg-gray-50 rounded-xl p-4 mb-6" style="height: 400px;">
                                <canvas id="revenueChart"></canvas>
                            </div>

                            <!-- Chart Summary -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-primary-50 rounded-lg p-4">
                                    <div class="text-sm font-medium text-primary-600 mb-1">Tổng doanh thu</div>
                                    <div class="text-2xl font-bold text-primary-900" id="total-revenue-period">0đ</div>
                                </div>
                                <div class="bg-success-50 rounded-lg p-4">
                                    <div class="text-sm font-medium text-success-600 mb-1">Trung bình/ngày</div>
                                    <div class="text-2xl font-bold text-success-900" id="avg-revenue-day">0đ</div>
                                </div>
                                <div class="bg-warning-50 rounded-lg p-4">
                                    <div class="text-sm font-medium text-warning-600 mb-1">Ngày cao nhất</div>
                                    <div class="text-2xl font-bold text-warning-900" id="highest-revenue-day">0đ</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-semibold text-gray-900">Đơn hàng gần đây</h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="overflow-x-auto">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Mã đơn</th>
                                            <th>Bàn</th>
                                            <th>Thời gian</th>
                                            <th>Tổng tiền</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-orders-table">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other pages will be added here -->
                <div id="tables" class="page">
                    <h2>Quản lý bàn - Coming Soon</h2>
                </div>

                <div id="menu" class="page">
                    <h2>Thực đơn - Coming Soon</h2>
                </div>

                <div id="orders" class="page">
                    <h2>Đơn hàng - Coming Soon</h2>
                </div>

                <div id="kitchen" class="page">
                    <h2>Nhà bếp - Coming Soon</h2>
                </div>

                <div id="inventory" class="page">
                    <h2>Kho hàng - Coming Soon</h2>
                </div>

                <div id="users" class="page">
                    <h2>Người dùng - Coming Soon</h2>
                </div>
            </div>
        </main>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Đăng nhập hệ thống</h3>
            </div>
            <form id="login-form" class="modal-body">
                <div class="form-group">
                    <label for="username" class="form-label">Tên đăng nhập</label>
                    <input type="text" id="username" class="form-input" required>
                </div>
                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <input type="password" id="password" class="form-input" required>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        Đăng nhập
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/modern-ui.js"></script>
    <script src="js/main.js"></script>

    <script>
        // Integration with existing logic
        document.addEventListener('pageChanged', (e) => {
            const page = e.detail.page;
            console.log(`Page changed to: ${page}`);

            // Load page-specific data
            if (page === 'dashboard') {
                loadDashboardData();
            }
            // Add other page handlers here
        });

        // Override notification manager to use modern notifications
        if (window.notifications) {
            window.notificationManager = {
                success: (msg) => window.notifications.success(msg),
                error: (msg) => window.notifications.error(msg),
                warning: (msg) => window.notifications.warning(msg),
                info: (msg) => window.notifications.info(msg)
            };
        }
    </script>
</body>
</html>
