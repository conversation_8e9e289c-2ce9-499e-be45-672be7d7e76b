// Global variables
let currentTableId = null;
let cart = [];
let menu = [];
let buffetMenu = null;
let socket = null;
let buffetPrice = null;
let tableInfo = null; // Thông tin bàn hiện tại
let selectedFreeDrink = null;
let isBuffetActive = false;

// DOM Elements
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const loading = document.getElementById('loading');
const menuCategories = document.getElementById('menu-categories');
const cartItems = document.getElementById('cart-items');
const cartSummary = document.getElementById('cart-summary');
const cartCount = document.getElementById('cart-count');
const totalAmount = document.getElementById('total-amount');
const currentOrder = document.getElementById('current-order');
const searchInput = document.getElementById('search-input');
const modal = document.getElementById('food-modal');
const notification = document.getElementById('notification');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, initializing app...');
    initializeApp();
});

async function initializeApp() {
    try {
        // Get table ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        currentTableId = urlParams.get('table_id');

        if (!currentTableId) {
            showError('Thiếu thông tin bàn. Vui lòng quét lại QR code.');
            return;
        }

        // Initialize Socket.IO
        initializeSocket();

        // Load table info
        await loadTableInfo();

        // Load menu
        await loadMenu();

        // Load current order
        await loadCurrentOrder();

        // Setup event listeners
        setupEventListeners();

        // Hide loading
        loading.classList.remove('show');

    } catch (error) {
        console.error('Error initializing app:', error);
        showError('Lỗi khi khởi tạo ứng dụng');
    }
}

function initializeSocket() {
    socket = io();

    socket.on('connect', () => {
        console.log('Connected to server');
        socket.emit('join_table', currentTableId);
    });

    socket.on('order_created', (data) => {
        showNotification(data.message || 'Đơn hàng đã được tạo thành công!');
        loadCurrentOrder();
        clearCart();
    });

    socket.on('order_status_updated', (data) => {
        showNotification('Trạng thái đơn hàng đã được cập nhật!');
        loadCurrentOrder();
    });
}

async function loadTableInfo() {
    try {
        const response = await fetch(`/api/customer/table/${currentTableId}`);
        if (!response.ok) throw new Error('Failed to load table info');

        tableInfo = await response.json();

        const tableNameElement = document.getElementById('table-name');
        if (tableNameElement) {
            tableNameElement.textContent = tableInfo.name;
        }

        console.log(`📋 Table info loaded:`, tableInfo);
        console.log(`🔍 Table is_buffet: ${tableInfo.is_buffet}`);

        // Đợi một chút để đảm bảo DOM đã sẵn sàng
        setTimeout(async () => {
            // Kiểm tra trạng thái bàn để hiển thị giao diện phù hợp
            if (tableInfo.is_buffet) {
                console.log(`🍽️ Bàn đang trong chế độ buffet - kiểm tra đơn hàng buffet hiện tại`);

                // Kiểm tra xem có đơn hàng buffet đang hoạt động không
                await loadCurrentOrder();

                // Nếu có đơn hàng buffet đang hoạt động, chuyển sang chế độ buffet active
                if (isBuffetActive) {
                    console.log(`🍽️ Có đơn hàng buffet đang hoạt động - hiển thị giao diện buffet active`);
                    hideOrderTypeSelection();
                    hideNormalOrderSection();
                    hideBuffetSection();
                    showBuffetActiveMode();
                    loadBuffetMenu();
                } else {
                    console.log(`🍽️ Bàn buffet nhưng chưa có đơn hàng - hiển thị giao diện chọn món buffet`);
                    hideOrderTypeSelection();
                    hideNormalOrderSection();
                    showBuffetSection();
                    hideBuffetActiveMode();
                    loadBuffetMenu();
                }
            } else {
                console.log(`🍽️ Bàn trống - hiển thị giao diện lựa chọn loại đặt món`);
                // Bàn trống - hiển thị giao diện lựa chọn
                showOrderTypeSelection();
                hideNormalOrderSection();
                hideBuffetSection();
                hideBuffetActiveMode();
                // Load giá buffet để hiển thị
                loadBuffetPrice();
            }
        }, 100);

    } catch (error) {
        console.error('Error loading table info:', error);
        const tableNameElement = document.getElementById('table-name');
        if (tableNameElement) {
            tableNameElement.textContent = `Bàn ${currentTableId}`;
        }
        // Mặc định hiển thị giao diện lựa chọn loại đặt món
        setTimeout(() => {
            showOrderTypeSelection();
            hideNormalOrderSection();
            hideBuffetSection();
            hideBuffetActiveMode();
            loadBuffetPrice();
        }, 100);
    }
}

async function loadBuffetPrice() {
    try {
        const response = await fetch('/api/customer/buffet-price');
        if (!response.ok) throw new Error('Failed to load buffet price');

        const data = await response.json();
        buffetPrice = data.price;

        // Update buffet price display
        const buffetPriceElements = document.querySelectorAll('#buffet-price, #buffet-price-display');
        buffetPriceElements.forEach(element => {
            element.textContent = data.formatted_price;
        });

    } catch (error) {
        console.error('Error loading buffet price:', error);
        const buffetPriceElements = document.querySelectorAll('#buffet-price, #buffet-price-display');
        buffetPriceElements.forEach(element => {
            element.textContent = '299.000 ₫/người';
        });
        buffetPrice = 299000; // Fallback price: 299,000 VND per person
    }
}

async function loadMenu() {
    try {
        loading.classList.add('show');

        const response = await fetch('/api/customer/menu');
        if (!response.ok) throw new Error('Failed to load menu');

        menu = await response.json();
        renderMenu();

    } catch (error) {
        console.error('Error loading menu:', error);
        showError('Lỗi khi tải menu');
    } finally {
        loading.classList.remove('show');
    }
}

function renderMenu(searchTerm = '') {
    menuCategories.innerHTML = '';

    menu.forEach(category => {
        const filteredFoods = category.foods.filter(food =>
            food.name.toLowerCase().includes(searchTerm.toLowerCase())
        );

        if (filteredFoods.length === 0) return;

        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'category';

        categoryDiv.innerHTML = `
            <div class="category-header">
                <i class="fas fa-utensils"></i> ${category.name}
            </div>
            <div class="foods-grid">
                ${filteredFoods.map(food => `
                    <div class="food-item" onclick="openFoodModal(${food.id})">
                        <div class="food-image-container">
                            <img src="${getImageUrl(food.image_url)}"
                                 alt="${food.name}" class="food-image"
                                 onload="this.style.display='block'"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                            <div class="no-image-placeholder" style="display:none">
                                <i class="fas fa-image"></i>
                                <span>Không có hình</span>
                            </div>
                        </div>
                        <div class="food-info">
                            <div class="food-name">${food.name}</div>
                            <div class="food-price">${formatPrice(food.price)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        menuCategories.appendChild(categoryDiv);
    });
}

function openFoodModal(foodId) {
    const food = findFoodById(foodId);
    if (!food) return;

    // Reset modal image state
    const modalImage = document.getElementById('modal-food-image');
    const modalPlaceholder = document.querySelector('.modal-no-image-placeholder');

    // Hide both initially
    modalImage.style.display = 'none';
    modalPlaceholder.style.display = 'none';

    // Set up image loading
    modalImage.onload = function() {
        this.style.display = 'block';
        modalPlaceholder.style.display = 'none';
    };

    modalImage.onerror = function() {
        this.style.display = 'none';
        modalPlaceholder.style.display = 'flex';
    };

    // Set image source (this will trigger onload or onerror)
    const imageUrl = getImageUrl(food.image_url);
    console.log('Modal image URL:', imageUrl);
    modalImage.src = imageUrl;

    // Set other modal content
    document.getElementById('modal-food-name').textContent = food.name;
    document.getElementById('modal-food-price').textContent = formatPrice(food.price);
    document.getElementById('modal-quantity').textContent = '1';

    // Store food data in modal
    modal.dataset.foodId = foodId;
    modal.dataset.foodPrice = food.price;

    modal.style.display = 'block';
}

function findFoodById(foodId) {
    for (const category of menu) {
        const food = category.foods.find(f => f.id === foodId);
        if (food) return food;
    }
    return null;
}

function getImageUrl(imageUrl) {
    if (!imageUrl) {
        return getDefaultImage();
    }

    // Nếu URL đã có domain, trả về nguyên vẹn
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        return imageUrl;
    }

    // Lấy host hiện tại (bao gồm IP và port) để điện thoại có thể truy cập
    const currentHost = window.location.host;
    const protocol = window.location.protocol;

    // Chuyển đổi port từ 3011 (table service) sang 3000 (api gateway)
    const apiGatewayHost = currentHost.replace(':3011', ':3000');
    const baseUrl = `${protocol}//${apiGatewayHost}`;

    // Nếu URL bắt đầu với /api/images, chuyển đổi thành URL đầy đủ đến Image Service
    if (imageUrl.startsWith('/api/images/')) {
        return `${baseUrl}${imageUrl}`;
    }

    // Nếu URL bắt đầu với /image-service, chuyển đổi sang Image Service
    if (imageUrl.startsWith('/image-service/uploads/')) {
        return `${baseUrl}${imageUrl.replace('/image-service/uploads/', '/api/images/')}`;
    }

    // Nếu chỉ là tên file, thêm prefix đầy đủ
    if (!imageUrl.includes('/') && imageUrl.includes('.')) {
        return `${baseUrl}/api/images/foods/${imageUrl}`;
    }

    // Mặc định trả về URL gốc
    return imageUrl;
}

function getDefaultImage() {
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjMwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNGNUY1RjUiLz48cmVjdCB4PSIxMjAiIHk9IjgwIiB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIGZpbGw9IiNEREREREQiLz48Y2lyY2xlIGN4PSIxMzUiIGN5PSI5NSIgcj0iNSIgZmlsbD0iI0JCQkJCQiIvPjxwYXRoIGQ9Ik0xMjUgMTEwTDE0MCAxMDBMMTU1IDExMEwxNzAgMTAwVjEyMEgxMjVWMTEwWiIgZmlsbD0iI0JCQkJCQiIvPjx0ZXh0IHg9IjE1MCIgeT0iMTQ1IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTk5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkjDrG5oIMSRYWkgZGnhu4duPC90ZXh0Pjwvc3ZnPg==';
}

function addToCart() {
    const foodId = parseInt(modal.dataset.foodId);
    const quantity = parseInt(document.getElementById('modal-quantity').textContent);
    const isBuffet = modal.dataset.isBuffet === 'true';

    if (isBuffet) {
        // Add food to buffet
        addFoodToBuffet(foodId, quantity);
    } else {
        // Normal cart functionality
        const food = findFoodById(foodId);
        if (!food) return;

        // Check if item already in cart
        const existingItem = cart.find(item => item.food_id === foodId);

        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            cart.push({
                food_id: foodId,
                name: food.name,
                price: food.price,
                image_url: food.image_url,
                quantity: quantity
            });
        }

        updateCartDisplay();
        showNotification(`Đã thêm ${food.name} vào giỏ hàng!`);
    }

    modal.style.display = 'none';
}

// Add food to buffet order
async function addFoodToBuffet(foodId, quantity) {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const tableKey = urlParams.get('key');

        const orderData = {
            table_id: parseInt(currentTableId),
            table_key: tableKey,
            items: [{ food_id: foodId, quantity: quantity }]
        };

        const response = await fetch('/api/customer/buffet-add-food', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Lỗi khi thêm món ăn');
        }

        const food = findBuffetFoodById(foodId);
        showNotification(`Đã thêm ${food ? food.name : 'món ăn'} vào buffet!`);

        // Reload current order to show new items
        loadCurrentOrder();

    } catch (error) {
        console.error('Error adding food to buffet:', error);
        showError(error.message || 'Lỗi khi thêm món ăn cho buffet');
    }
}

function updateCartDisplay() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    cartCount.textContent = totalItems;
    totalAmount.textContent = formatPrice(total);

    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>Giỏ hàng trống</p>
                <p>Hãy chọn món ăn từ menu</p>
            </div>
        `;
        cartSummary.style.display = 'none';
    } else {
        cartItems.innerHTML = cart.map((item, index) => `
            <div class="cart-item">
                <div class="cart-item-image-container">
                    <img src="${getImageUrl(item.image_url)}"
                         alt="${item.name}" class="cart-item-image"
                         onload="this.style.display='block'"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                    <div class="cart-no-image-placeholder" style="display:none">
                        <i class="fas fa-image"></i>
                    </div>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${formatPrice(item.price)}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="qty-btn" onclick="updateCartItemQuantity(${index}, -1)">-</button>
                    <span class="cart-item-quantity">${item.quantity}</span>
                    <button class="qty-btn" onclick="updateCartItemQuantity(${index}, 1)">+</button>
                    <button class="remove-item" onclick="removeCartItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        cartSummary.style.display = 'block';
    }
}

function updateCartItemQuantity(index, change) {
    cart[index].quantity += change;

    if (cart[index].quantity <= 0) {
        cart.splice(index, 1);
    }

    updateCartDisplay();
}

function removeCartItem(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

function clearCart() {
    cart = [];
    updateCartDisplay();
}

// Payment functions
function showPaymentModal(orderId, amount) {
    const paymentModal = document.getElementById('payment-modal');
    if (!paymentModal) {
        console.error('Payment modal not found');
        return;
    }

    // Set order info
    document.getElementById('payment-order-id').textContent = orderId;
    document.getElementById('payment-amount').textContent = formatPrice(amount);
    document.getElementById('payment-amount-value').value = amount;

    // Reset payment method selection
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => {
        radio.checked = false;
    });

    // Show modal
    paymentModal.style.display = 'block';
}

function selectPaymentMethod(method) {
    const cashSection = document.getElementById('cash-payment-section');
    const momoSection = document.getElementById('momo-payment-section');

    if (method === 'cash') {
        cashSection.style.display = 'block';
        momoSection.style.display = 'none';
    } else if (method === 'momo') {
        cashSection.style.display = 'none';
        momoSection.style.display = 'block';
    }
}

async function processPayment() {
    const selectedMethod = document.querySelector('input[name="payment-method"]:checked');
    if (!selectedMethod) {
        showError('Vui lòng chọn phương thức thanh toán');
        return;
    }

    const orderId = document.getElementById('payment-order-id').textContent;
    const amount = parseFloat(document.getElementById('payment-amount-value').value);

    if (selectedMethod.value === 'cash') {
        await processCashPayment(orderId, amount);
    } else if (selectedMethod.value === 'momo') {
        await processMoMoPayment(orderId, amount);
    }
}

async function processCashPayment(orderId, amount) {
    const cashReceived = parseFloat(document.getElementById('cash-received').value);

    if (!cashReceived || cashReceived < amount) {
        showError('Số tiền nhận phải lớn hơn hoặc bằng tổng tiền');
        return;
    }

    try {
        const response = await fetch('/api/payments/cash', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                orderId: parseInt(orderId),
                amount: amount,
                cashReceived: cashReceived
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            const change = result.cashChange;
            showNotification(`Thanh toán thành công! Tiền thừa: ${formatPrice(change)}`);
            closePaymentModal();
            loadCurrentOrder(); // Refresh order status
        } else {
            showError(result.message || 'Lỗi khi xử lý thanh toán tiền mặt');
        }
    } catch (error) {
        console.error('Error processing cash payment:', error);
        showError('Lỗi khi xử lý thanh toán tiền mặt');
    }
}

async function processMoMoPayment(orderId, amount) {
    try {
        const tableKey = new URLSearchParams(window.location.search).get('key');

        const response = await fetch('/api/payments/momo/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                orderId: parseInt(orderId),
                amount: amount,
                orderInfo: `Thanh toán đơn hàng #${orderId} - Bàn ${currentTableId}`,
                tableId: currentTableId
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            // Redirect to MoMo payment page
            window.open(result.payUrl, '_blank');
            showNotification('Đang chuyển đến trang thanh toán MoMo...');
            closePaymentModal();

            // Start checking payment status
            checkPaymentStatus(orderId);
        } else {
            showError(result.message || 'Lỗi khi tạo thanh toán MoMo');
        }
    } catch (error) {
        console.error('Error processing MoMo payment:', error);
        showError('Lỗi khi xử lý thanh toán MoMo');
    }
}

function checkPaymentStatus(orderId) {
    const checkInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/payments/status/${orderId}`);
            const status = await response.json();

            if (status.payment_status === 'paid') {
                clearInterval(checkInterval);
                showNotification('Thanh toán MoMo thành công!');
                loadCurrentOrder(); // Refresh order status
            }
        } catch (error) {
            console.error('Error checking payment status:', error);
        }
    }, 3000); // Check every 3 seconds

    // Stop checking after 5 minutes
    setTimeout(() => {
        clearInterval(checkInterval);
    }, 300000);
}

function closePaymentModal() {
    const paymentModal = document.getElementById('payment-modal');
    if (paymentModal) {
        paymentModal.style.display = 'none';
    }

    // Reset form
    document.getElementById('cash-received').value = '';
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => {
        radio.checked = false;
    });
    document.getElementById('cash-payment-section').style.display = 'none';
    document.getElementById('momo-payment-section').style.display = 'none';
}

function selectOrderType(type) {
    // Kiểm tra elements tồn tại
    const orderTypeSelection = document.getElementById('order-type-selection');
    const normalOrderSection = document.getElementById('normal-order-section');
    const buffetOrderSection = document.getElementById('buffet-order-section');

    if (!orderTypeSelection || !normalOrderSection || !buffetOrderSection) {
        console.error('Không tìm thấy elements để chuyển đổi giao diện');
        return;
    }

    // Hide order type selection
    orderTypeSelection.style.display = 'none';

    if (type === 'normal') {
        // Show normal order section
        normalOrderSection.style.display = 'block';
        buffetOrderSection.style.display = 'none';
    } else if (type === 'buffet') {
        // Show buffet order section
        normalOrderSection.style.display = 'none';
        buffetOrderSection.style.display = 'block';

        // Load buffet packages first
        loadBuffetPackages();
    }
}

function backToSelection() {
    console.log(`🔙 backToSelection called - isBuffetActive: ${isBuffetActive}`);

    // Nếu buffet đang hoạt động, không cho phép quay lại
    if (isBuffetActive) {
        console.log(`❌ Blocking back to selection - buffet is active`);
        showNotification('Buffet đang hoạt động. Không thể thay đổi loại đặt món.');
        return;
    }

    // Kiểm tra elements tồn tại
    const orderTypeSelection = document.getElementById('order-type-selection');
    const normalOrderSection = document.getElementById('normal-order-section');
    const buffetOrderSection = document.getElementById('buffet-order-section');

    if (!orderTypeSelection || !normalOrderSection || !buffetOrderSection) {
        console.error('Không tìm thấy elements để quay lại lựa chọn');
        return;
    }

    // Show order type selection
    orderTypeSelection.style.display = 'block';

    // Hide both order sections
    normalOrderSection.style.display = 'none';
    buffetOrderSection.style.display = 'none';
}

// Load buffet packages
async function loadBuffetPackages() {
    try {
        console.log('🍽️ Đang tải buffet packages...');
        const response = await fetch('/api/buffet-packages');
        if (!response.ok) throw new Error('Failed to load buffet packages');

        const packages = await response.json();
        console.log('✅ Đã tải buffet packages:', packages);

        renderBuffetPackages(packages);

    } catch (error) {
        console.error('Error loading buffet packages:', error);
        showError('Lỗi khi tải danh sách gói buffet');
    }
}

// Render buffet packages grid
function renderBuffetPackages(packages) {
    const buffetPackagesGrid = document.getElementById('buffet-packages-grid');
    if (!buffetPackagesGrid) {
        console.error('buffet-packages-grid element not found');
        return;
    }

    buffetPackagesGrid.innerHTML = '';

    packages.forEach(pkg => {
        const packageCard = document.createElement('div');
        packageCard.className = `buffet-package-card ${pkg.popular ? 'popular' : ''}`;
        packageCard.onclick = () => selectBuffetPackage(pkg);

        packageCard.innerHTML = `
            <div class="buffet-package-header">
                <div class="buffet-package-icon ${pkg.color}">
                    <i class="${pkg.icon}"></i>
                </div>
                <h3 class="buffet-package-name">${pkg.name}</h3>
                <p class="buffet-package-description">${pkg.description}</p>
                <div class="buffet-package-price">${formatPrice(pkg.price)}/người</div>
            </div>
            <div class="buffet-package-features">
                <ul>
                    ${pkg.features.map(feature => `
                        <li>
                            <i class="fas fa-check"></i>
                            ${feature}
                        </li>
                    `).join('')}
                </ul>
            </div>
            <button class="select-buffet-btn">
                <i class="fas fa-arrow-right"></i>
                Chọn gói này
            </button>
        `;

        buffetPackagesGrid.appendChild(packageCard);
    });
}

// Select buffet package
function selectBuffetPackage(pkg) {
    console.log('Selected buffet package:', pkg);

    // Store selected package
    window.selectedBuffetPackage = pkg;

    // Update buffet price
    buffetPrice = pkg.price;

    // Update selected buffet details
    const selectedBuffetName = document.getElementById('selected-buffet-name');
    const selectedBuffetFeatures = document.getElementById('selected-buffet-features');
    const buffetPriceDisplay = document.getElementById('buffet-price-display');

    if (selectedBuffetName) {
        selectedBuffetName.textContent = pkg.name;
    }

    if (selectedBuffetFeatures) {
        selectedBuffetFeatures.innerHTML = pkg.features.map(feature => `
            <p><i class="fas fa-check"></i> ${feature}</p>
        `).join('');
    }

    if (buffetPriceDisplay) {
        buffetPriceDisplay.textContent = formatPrice(pkg.price) + '/người';
    }

    // Hide packages grid and show buffet selection
    const buffetPackagesGrid = document.getElementById('buffet-packages-grid');
    const buffetSelection = document.getElementById('buffet-selection');

    if (buffetPackagesGrid) {
        buffetPackagesGrid.style.display = 'none';
    }

    if (buffetSelection) {
        buffetSelection.style.display = 'block';
    }

    // Load free drinks for this package
    loadBuffetMenu();
}

// Back to packages selection
function backToPackages() {
    const buffetPackagesGrid = document.getElementById('buffet-packages-grid');
    const buffetSelection = document.getElementById('buffet-selection');

    if (buffetPackagesGrid) {
        buffetPackagesGrid.style.display = 'grid';
    }

    if (buffetSelection) {
        buffetSelection.style.display = 'none';
    }

    // Reset selected package
    window.selectedBuffetPackage = null;
    selectedFreeDrink = null;

    // Disable order button
    const orderBuffetBtn = document.getElementById('order-buffet-btn');
    if (orderBuffetBtn) {
        orderBuffetBtn.disabled = true;
    }
}

// Load buffet menu with drinks separated
async function loadBuffetMenu() {
    try {
        console.log('🍹 Đang tải buffet menu...');
        const response = await fetch('/api/customer/buffet-menu');
        if (!response.ok) throw new Error('Failed to load buffet menu');

        buffetMenu = await response.json();
        console.log('✅ Đã tải buffet menu:', buffetMenu);

        renderFreeDrinks();

        // If buffet is already active, render food categories
        if (isBuffetActive) {
            console.log('🍽️ Buffet đang hoạt động, render food categories...');
            renderBuffetFoodCategories();
        }

    } catch (error) {
        console.error('Error loading buffet menu:', error);
        showError('Lỗi khi tải menu buffet');
    }
}

// Render free drinks selection
function renderFreeDrinks() {
    const freeDrinksGrid = document.getElementById('free-drinks-grid');
    console.log('renderFreeDrinks called:', { freeDrinksGrid, buffetMenu }); // Debug log

    if (!freeDrinksGrid) {
        console.error('free-drinks-grid element not found');
        return;
    }

    if (!buffetMenu) {
        console.error('buffetMenu is null');
        return;
    }

    if (!buffetMenu.drinks) {
        console.error('buffetMenu.drinks is null or undefined');
        return;
    }

    console.log('Drinks categories:', buffetMenu.drinks); // Debug log
    freeDrinksGrid.innerHTML = '';

    buffetMenu.drinks.forEach(category => {
        console.log('Processing drink category:', category); // Debug log
        category.foods.forEach(drink => {
            const drinkDiv = document.createElement('div');
            drinkDiv.className = 'drink-item';
            drinkDiv.onclick = () => selectFreeDrink(drink.id);

            drinkDiv.innerHTML = `
                <img src="${getImageUrl(drink.image_url)}"
                     alt="${drink.name}" class="drink-image"
                     onload="this.style.display='block'"
                     onerror="this.style.display='none'">
                <div class="drink-name">${drink.name}</div>
                <div class="drink-price">${formatPrice(drink.price)}</div>
            `;

            freeDrinksGrid.appendChild(drinkDiv);
        });
    });

    console.log('Drinks rendered, total items:', freeDrinksGrid.children.length); // Debug log
}

// Select free drink
function selectFreeDrink(drinkId) {
    selectedFreeDrink = drinkId;

    // Update UI
    document.querySelectorAll('.drink-item').forEach(item => {
        item.classList.remove('selected');
    });

    event.currentTarget.classList.add('selected');

    // Enable buffet order button
    const orderBuffetBtn = document.getElementById('order-buffet-btn');
    if (orderBuffetBtn) {
        orderBuffetBtn.disabled = false;
    }
}

async function orderBuffet() {
    if (!selectedFreeDrink) {
        showError('Vui lòng chọn món nước miễn phí!');
        return;
    }

    if (!window.selectedBuffetPackage) {
        showError('Vui lòng chọn gói buffet!');
        return;
    }

    try {
        // Lấy key từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const tableKey = urlParams.get('key');

        const orderData = {
            table_id: parseInt(currentTableId),
            table_key: tableKey,
            free_drink_id: selectedFreeDrink,
            buffet_price: buffetPrice,
            buffet_package_id: window.selectedBuffetPackage.id,
            buffet_package_name: window.selectedBuffetPackage.name
        };

        const response = await fetch('/api/customer/buffet-orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Lỗi khi đặt buffet');
        }

        const order = await response.json();
        showNotification('Đặt buffet thành công! Bạn có thể gọi thêm món ăn.');

        // Cập nhật trạng thái bàn thành buffet
        tableInfo.is_buffet = true;

        // Switch to buffet active mode
        console.log(`🍽️ Setting isBuffetActive = true in orderBuffet()`);
        isBuffetActive = true;
        showBuffetActiveMode();

        // Hide order type selection and show buffet section
        hideOrderTypeSelection();
        showBuffetSection();

        // Switch to orders tab
        switchTab('orders');

    } catch (error) {
        console.error('Error ordering buffet:', error);
        showError(error.message || 'Lỗi khi đặt buffet');
    }
}

// Hide order type selection
function hideOrderTypeSelection() {
    const orderTypeSelection = document.getElementById('order-type-selection');
    if (orderTypeSelection) {
        orderTypeSelection.style.display = 'none';
    }
}

// Show order type selection
function showOrderTypeSelection() {
    const orderTypeSelection = document.getElementById('order-type-selection');
    const normalOrderCard = document.getElementById('normal-order-card');
    const buffetOrderCard = document.getElementById('buffet-order-card');

    if (orderTypeSelection) {
        orderTypeSelection.style.display = 'block';
    }

    // Hiển thị cả hai tùy chọn để khách hàng lựa chọn
    if (normalOrderCard) normalOrderCard.style.display = 'block';
    if (buffetOrderCard) buffetOrderCard.style.display = 'block';
}

// Show buffet section
function showBuffetSection() {
    const buffetOrderSection = document.getElementById('buffet-order-section');
    if (buffetOrderSection) {
        buffetOrderSection.style.display = 'block';
        console.log(`✅ Made buffet-order-section visible in showBuffetSection()`);
        // Load buffet price when showing buffet section
        loadBuffetPrice();
    }
}

// Hide buffet section
function hideBuffetSection() {
    const buffetOrderSection = document.getElementById('buffet-order-section');
    if (buffetOrderSection) {
        buffetOrderSection.style.display = 'none';
    }
}

// Show buffet active mode
function showBuffetActiveMode() {
    console.log(`🔍 showBuffetActiveMode() called`);

    // First ensure buffet order section is visible
    const buffetOrderSection = document.getElementById('buffet-order-section');
    if (buffetOrderSection) {
        buffetOrderSection.style.display = 'block';
        console.log(`✅ Made buffet-order-section visible`);
    }

    const buffetSelection = document.getElementById('buffet-selection');
    const buffetActive = document.getElementById('buffet-active');

    console.log(`🔍 Elements found:`, {
        buffetOrderSection: !!buffetOrderSection,
        buffetSelection: !!buffetSelection,
        buffetActive: !!buffetActive,
        buffetMenu: !!buffetMenu
    });

    if (buffetSelection && buffetActive) {
        buffetSelection.style.display = 'none';
        buffetActive.style.display = 'block';
        console.log(`✅ Switched to buffet active mode`);

        // Kiểm tra element ngay lập tức
        const buffetFoodCategories = document.getElementById('buffet-food-categories');
        console.log(`🔍 buffetFoodCategories element:`, !!buffetFoodCategories);

        // Load food categories for buffet only if buffet menu is available
        if (buffetMenu && buffetMenu.foods) {
            console.log(`🍽️ Rendering buffet food categories...`);
            renderBuffetFoodCategories();
        } else {
            console.warn('Buffet menu not loaded yet, loading now...');
            // Load buffet menu if not available
            loadBuffetMenu().then(() => {
                if (buffetMenu && buffetMenu.foods) {
                    console.log(`🍽️ Buffet menu loaded, now rendering categories...`);
                    renderBuffetFoodCategories();
                }
            });
        }
    } else {
        console.error('❌ Cannot find buffet elements:', {
            buffetSelection: !!buffetSelection,
            buffetActive: !!buffetActive
        });
    }
}

// Hide buffet active mode
function hideBuffetActiveMode() {
    const buffetSelection = document.getElementById('buffet-selection');
    const buffetActive = document.getElementById('buffet-active');

    if (buffetSelection && buffetActive) {
        buffetSelection.style.display = 'block';
        buffetActive.style.display = 'none';
    }
}

// Show normal order section
function showNormalOrderSection() {
    const normalOrderSection = document.getElementById('normal-order-section');
    if (normalOrderSection) {
        normalOrderSection.style.display = 'block';
    }
}

// Hide normal order section
function hideNormalOrderSection() {
    const normalOrderSection = document.getElementById('normal-order-section');
    if (normalOrderSection) {
        normalOrderSection.style.display = 'none';
    }
}

// Hide all sections - để loadCurrentOrder quyết định hiển thị gì
function hideAllSections() {
    hideOrderTypeSelection();
    hideNormalOrderSection();
    hideBuffetSection();
    hideBuffetActiveMode();
}

// Render food categories for buffet (only foods, no drinks)
function renderBuffetFoodCategories() {
    const buffetFoodCategories = document.getElementById('buffet-food-categories');
    console.log('🔍 buffetFoodCategories element:', buffetFoodCategories);
    console.log('🔍 buffetMenu:', buffetMenu);

    if (!buffetFoodCategories) {
        console.error('❌ buffet-food-categories element not found!');
        return;
    }

    if (!buffetMenu) {
        console.error('❌ buffetMenu is null!');
        return;
    }

    buffetFoodCategories.innerHTML = '';

    // buffetMenu structure: {foods: Array(categories), drinks: Array}
    // Each category in foods has: {id, name, foods: Array}
    if (!buffetMenu.foods || !Array.isArray(buffetMenu.foods)) {
        console.error('❌ Invalid buffet menu structure:', buffetMenu);
        return;
    }

    console.log(`📋 Found ${buffetMenu.foods.length} food categories`);

    buffetMenu.foods.forEach((category, index) => {
        console.log(`🍽️ Processing category ${index + 1}: ${category.name} with ${category.foods?.length || 0} foods`);

        if (!category.foods || category.foods.length === 0) {
            console.log(`⚠️ Skipping empty category: ${category.name}`);
            return;
        }

        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'category';

        categoryDiv.innerHTML = `
            <div class="category-header">
                <i class="fas fa-utensils"></i> ${category.name}
            </div>
            <div class="foods-grid">
                ${category.foods.map(food => `
                    <div class="food-item" onclick="openBuffetFoodModal(${food.id})">
                        <div class="food-image-container">
                            <img src="${getImageUrl(food.image_url)}"
                                 alt="${food.name}" class="food-image"
                                 onload="this.style.display='block'"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                            <div class="no-image-placeholder" style="display:none">
                                <i class="fas fa-image"></i>
                                <span>Không có hình</span>
                            </div>
                        </div>
                        <div class="food-info">
                            <div class="food-name">${food.name}</div>
                            <div class="food-price">MIỄN PHÍ</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        buffetFoodCategories.appendChild(categoryDiv);
        console.log(`✅ Added category: ${category.name}`);
    });

    console.log(`✅ Buffet food categories rendered successfully!`);
    console.log(`🔍 Final buffetFoodCategories innerHTML length:`, buffetFoodCategories.innerHTML.length);
    console.log(`🔍 Final buffetFoodCategories children count:`, buffetFoodCategories.children.length);
}

// Open buffet food modal (for adding to buffet)
function openBuffetFoodModal(foodId) {
    const food = findBuffetFoodById(foodId);
    if (!food) return;

    // Similar to openFoodModal but for buffet
    const modalImage = document.getElementById('modal-food-image');
    const modalPlaceholder = document.querySelector('.modal-no-image-placeholder');

    modalImage.style.display = 'none';
    modalPlaceholder.style.display = 'none';

    modalImage.onload = function() {
        this.style.display = 'block';
        modalPlaceholder.style.display = 'none';
    };

    modalImage.onerror = function() {
        this.style.display = 'none';
        modalPlaceholder.style.display = 'flex';
    };

    const imageUrl = getImageUrl(food.image_url);
    modalImage.src = imageUrl;

    document.getElementById('modal-food-name').textContent = food.name;
    document.getElementById('modal-food-price').textContent = 'MIỄN PHÍ (Buffet)';
    document.getElementById('modal-quantity').textContent = '1';

    // Store food data in modal
    modal.dataset.foodId = foodId;
    modal.dataset.isBuffet = 'true';

    modal.style.display = 'block';
}

// Find food by ID in buffet menu
function findBuffetFoodById(foodId) {
    if (!buffetMenu || !buffetMenu.foods) return null;

    for (const category of buffetMenu.foods) {
        if (category.foods) {
            const food = category.foods.find(f => f.id === foodId);
            if (food) return food;
        }
    }
    return null;
}

async function placeOrder() {
    if (cart.length === 0) {
        showError('Giỏ hàng trống!');
        return;
    }

    try {
        // Lấy key từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const tableKey = urlParams.get('key');

        const orderData = {
            table_id: parseInt(currentTableId),
            table_key: tableKey, // Thêm key vào request
            items: cart.map(item => ({
                food_id: item.food_id,
                quantity: item.quantity
            }))
        };

        const response = await fetch('/api/customer/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Lỗi khi đặt món');
        }

        const order = await response.json();
        showNotification('Đặt món thành công! Đơn hàng đã được gửi đến nhà bếp.');

        // Switch to orders tab
        switchTab('orders');

    } catch (error) {
        console.error('Error placing order:', error);
        showError(error.message || 'Lỗi khi đặt món');
    }
}

async function loadCurrentOrder() {
    try {
        // Reset buffet state trước khi load
        console.log(`🔄 Reset buffet state - isBuffetActive was: ${isBuffetActive}`);
        isBuffetActive = false;

        // Lấy key từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const tableKey = urlParams.get('key');

        // Tạo URL với key parameter và thêm timestamp để tránh cache
        let url = `/api/customer/orders/${currentTableId}`;
        if (tableKey) {
            url += `?key=${encodeURIComponent(tableKey)}`;
        }
        url += `${url.includes('?') ? '&' : '?'}t=${Date.now()}`;

        console.log(`🔍 Đang tải đơn hàng từ: ${url}`);

        const response = await fetch(url, {
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache'
            }
        });
        if (!response.ok) throw new Error('Failed to load order');

        const order = await response.json();

        if (!order) {
            console.log(`🍽️ Không có đơn hàng - giữ nguyên giao diện hiện tại`);
            // Không có đơn hàng - giữ nguyên giao diện đã được thiết lập trong loadTableInfo()
            // Không thay đổi giao diện ở đây

            currentOrder.innerHTML = `
                <div class="no-orders">
                    <i class="fas fa-receipt"></i>
                    <p>Chưa có đơn hàng nào</p>
                    <p>Hãy đặt món từ menu</p>
                </div>
            `;
            return;
        }

        console.log(`✅ Đã tải đơn hàng #${order.id}:`, order);
        console.log(`🔍 DEBUG - is_buffet: ${order.is_buffet}, payment_status: ${order.payment_status}, status: ${order.status}`);

        // Chỉ cập nhật trạng thái buffet active nếu:
        // 1. Bàn là bàn buffet (tableInfo.is_buffet = true)
        // 2. Có đơn hàng buffet đang hoạt động
        if (tableInfo && tableInfo.is_buffet && order.is_buffet && order.status === 'Đang phục vụ') {
            console.log(`🍽️ Bàn buffet có đơn hàng đang hoạt động - chuyển sang chế độ buffet active`);
            isBuffetActive = true;

            // Chỉ hiển thị buffet active mode nếu đang ở tab menu
            const currentTab = document.querySelector('.tab-btn.active')?.dataset.tab;
            if (currentTab === 'menu') {
                hideOrderTypeSelection();
                hideNormalOrderSection();
                hideBuffetSection();
                showBuffetActiveMode();

                // Load buffet menu if not already loaded
                if (!buffetMenu) {
                    await loadBuffetMenu();
                }
            }
        }
        // Không thay đổi giao diện cho các trường hợp khác - giữ nguyên giao diện đã được thiết lập

        renderCurrentOrder(order);

    } catch (error) {
        console.error('Error loading current order:', error);
    }
}

// Function để chỉ load nội dung đơn hàng mà không thay đổi giao diện
async function loadCurrentOrderContent() {
    try {
        const response = await fetch(`/api/customer/orders/${currentTableKey}`);
        if (!response.ok) throw new Error('Failed to load order');

        const order = await response.json();

        if (!order) {
            currentOrder.innerHTML = `
                <div class="no-orders">
                    <i class="fas fa-receipt"></i>
                    <p>Chưa có đơn hàng nào</p>
                    <p>Hãy đặt món từ menu</p>
                </div>
            `;
            return;
        }

        renderCurrentOrder(order);

    } catch (error) {
        console.error('Error loading current order content:', error);
    }
}

function renderCurrentOrder(order) {
    const statusClass = getStatusClass(order.status);
    const statusText = getStatusText(order.status);
    const paymentStatusClass = getPaymentStatusClass(order.payment_status);
    const paymentStatusText = getPaymentStatusText(order.payment_status);

    // Determine if payment button should be shown
    // For buffet orders, allow payment immediately when status is "Đang phục vụ"
    // For regular orders, only allow payment when status is "Hoàn thành"
    const showPaymentButton = order.payment_status === 'unpaid' &&
                             (order.status === 'Hoàn thành' ||
                              (order.is_buffet && order.status === 'Đang phục vụ'));

    console.log(`🔍 DEBUG Payment Button Logic:
        - payment_status: ${order.payment_status}
        - status: ${order.status}
        - is_buffet: ${order.is_buffet}
        - showPaymentButton: ${showPaymentButton}`);

    currentOrder.innerHTML = `
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">Đơn hàng #${order.id}</div>
                <div class="order-status ${statusClass}">${statusText}</div>
            </div>
            <div class="payment-status-row">
                <span>Trạng thái thanh toán:</span>
                <span class="payment-status ${paymentStatusClass}">${paymentStatusText}</span>
            </div>
            <div class="order-items">
                ${order.details.map(item => `
                    <div class="order-item">
                        <span>${item.food_name} x${item.quantity}</span>
                        <span>${formatPrice(item.price * item.quantity)}</span>
                    </div>
                `).join('')}
            </div>
            <div class="order-total">
                Tổng cộng: ${formatPrice(order.total)}
            </div>
            ${showPaymentButton ? `
                <div class="payment-section">
                    <button class="payment-btn" onclick="showPaymentModal(${order.id}, ${order.total})">
                        <i class="fas fa-credit-card"></i>
                        Thanh toán
                    </button>
                </div>
            ` : ''}
        </div>
    `;
}

function getStatusClass(status) {
    switch (status) {
        case 'Đang phục vụ': return 'pending';
        case 'Đang chế biến': return 'preparing';
        case 'Hoàn thành': return 'completed';
        default: return 'pending';
    }
}

function getStatusText(status) {
    return status || 'Đang xử lý';
}

function getPaymentStatusClass(paymentStatus) {
    switch (paymentStatus) {
        case 'paid': return 'paid';
        case 'pending': return 'pending';
        case 'unpaid': return 'unpaid';
        default: return 'unpaid';
    }
}

function getPaymentStatusText(paymentStatus) {
    switch (paymentStatus) {
        case 'paid': return 'Đã thanh toán';
        case 'pending': return 'Đang xử lý';
        case 'unpaid': return 'Chưa thanh toán';
        default: return 'Chưa thanh toán';
    }
}

function setupEventListeners() {
    // Tab switching
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabName = btn.dataset.tab;
            switchTab(tabName);
        });
    });

    // Search
    searchInput.addEventListener('input', (e) => {
        renderMenu(e.target.value);
    });

    // Modal controls
    document.getElementById('decrease-qty').addEventListener('click', () => {
        const qtyElement = document.getElementById('modal-quantity');
        let qty = parseInt(qtyElement.textContent);
        if (qty > 1) {
            qtyElement.textContent = qty - 1;
        }
    });

    document.getElementById('increase-qty').addEventListener('click', () => {
        const qtyElement = document.getElementById('modal-quantity');
        let qty = parseInt(qtyElement.textContent);
        qtyElement.textContent = qty + 1;
    });

    document.getElementById('add-to-cart-btn').addEventListener('click', addToCart);

    // Close modal
    document.querySelector('.close').addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Place order
    document.getElementById('place-order-btn').addEventListener('click', placeOrder);

    // Refresh orders
    document.getElementById('refresh-orders').addEventListener('click', loadCurrentOrder);

    // Order type selection (chỉ cho bàn thường)
    const selectTypeBtns = document.querySelectorAll('.select-type-btn');
    selectTypeBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const type = e.target.dataset.type;
            selectOrderType(type);
        });
    });

    // Back to selection buttons
    const backToSelectionBtn = document.getElementById('back-to-selection');
    if (backToSelectionBtn) {
        backToSelectionBtn.addEventListener('click', backToSelection);
    }

    const backToSelectionBuffetBtn = document.getElementById('back-to-selection-buffet');
    if (backToSelectionBuffetBtn) {
        backToSelectionBuffetBtn.addEventListener('click', backToSelection);
    }

    // Order buffet button
    const orderBuffetBtn = document.getElementById('order-buffet-btn');
    if (orderBuffetBtn) {
        orderBuffetBtn.addEventListener('click', orderBuffet);
    }

    // Back to packages button
    const backToPackagesBtn = document.getElementById('back-to-packages-btn');
    if (backToPackagesBtn) {
        backToPackagesBtn.addEventListener('click', backToPackages);
    }
}

function switchTab(tabName) {
    // Update tab buttons
    tabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Update tab contents
    tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });

    // Load data if needed
    if (tabName === 'orders') {
        // Chỉ render lại order content mà không thay đổi giao diện menu
        loadCurrentOrderContent();
    } else if (tabName === 'menu') {
        // Khi chuyển về tab menu, cập nhật giao diện dựa trên trạng thái buffet
        updateMenuInterface();
    }
}

// Function để cập nhật giao diện menu dựa trên trạng thái bàn
function updateMenuInterface() {
    console.log(`🔍 updateMenuInterface() called:`, {
        tableInfo: tableInfo,
        'tableInfo.is_buffet': tableInfo?.is_buffet,
        isBuffetActive: isBuffetActive
    });

    if (tableInfo && tableInfo.is_buffet && isBuffetActive) {
        console.log(`🍽️ Tab menu - Bàn buffet đang hoạt động, hiển thị buffet active mode`);
        hideOrderTypeSelection();
        hideNormalOrderSection();
        hideBuffetSection();
        showBuffetActiveMode();

        // Đảm bảo buffet menu được load
        if (!buffetMenu) {
            console.log(`🔄 Loading buffet menu...`);
            loadBuffetMenu();
        }
    } else if (tableInfo && tableInfo.is_buffet && !isBuffetActive) {
        console.log(`🍽️ Tab menu - Bàn buffet chưa hoạt động, hiển thị giao diện chọn món buffet`);
        hideOrderTypeSelection();
        hideNormalOrderSection();
        showBuffetSection();
        hideBuffetActiveMode();

        // Load buffet menu để hiển thị
        if (!buffetMenu) {
            console.log(`🔄 Loading buffet menu...`);
            loadBuffetMenu();
        }
    } else {
        console.log(`🍽️ Tab menu - Bàn thường, hiển thị giao diện lựa chọn`);
        showOrderTypeSelection();
        hideNormalOrderSection();
        hideBuffetSection();
        hideBuffetActiveMode();
    }
}

function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(price);
}

function showNotification(message) {
    document.getElementById('notification-message').textContent = message;
    notification.classList.add('show');

    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

function showError(message) {
    // You can customize this to show errors differently
    alert(message);
}

// Payment functions
function showPaymentModal(orderId, amount) {
    const paymentModal = document.getElementById('payment-modal');
    document.getElementById('payment-order-id').textContent = orderId;
    document.getElementById('payment-amount').textContent = formatPrice(amount);
    document.getElementById('payment-amount-value').value = amount;

    // Reset form
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => radio.checked = false);
    document.getElementById('cash-received').value = '';
    document.getElementById('cash-payment-section').style.display = 'none';
    document.getElementById('momo-payment-section').style.display = 'none';

    paymentModal.style.display = 'block';
}

function closePaymentModal() {
    document.getElementById('payment-modal').style.display = 'none';
}

function selectPaymentMethod(method) {
    // Hide all payment sections
    document.getElementById('cash-payment-section').style.display = 'none';
    document.getElementById('momo-payment-section').style.display = 'none';

    // Show selected payment section
    if (method === 'cash') {
        document.getElementById('cash-payment-section').style.display = 'block';
    } else if (method === 'momo') {
        document.getElementById('momo-payment-section').style.display = 'block';
    }
}

async function processPayment() {
    const selectedMethod = document.querySelector('input[name="payment-method"]:checked');
    if (!selectedMethod) {
        showError('Vui lòng chọn phương thức thanh toán!');
        return;
    }

    const orderId = document.getElementById('payment-order-id').textContent;
    const amount = parseFloat(document.getElementById('payment-amount-value').value);
    const method = selectedMethod.value;

    try {
        if (method === 'cash') {
            await processCashPayment(orderId, amount);
        } else if (method === 'momo') {
            await processMoMoPayment(orderId, amount);
        }
    } catch (error) {
        console.error('Payment error:', error);
        showError(error.message || 'Lỗi khi xử lý thanh toán');
    }
}

async function processCashPayment(orderId, amount) {
    const cashReceived = parseFloat(document.getElementById('cash-received').value);

    if (!cashReceived || cashReceived < amount) {
        showError('Số tiền nhận phải lớn hơn hoặc bằng tổng tiền đơn hàng!');
        return;
    }

    const paymentData = {
        order_id: parseInt(orderId),
        payment_method: 'cash',
        amount: amount,
        cash_received: cashReceived
    };

    const response = await fetch('/api/customer/payments/cash', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData)
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Lỗi khi thanh toán tiền mặt');
    }

    const result = await response.json();

    // Show success message with change
    const change = cashReceived - amount;
    if (change > 0) {
        showNotification(`Thanh toán thành công! Tiền thừa: ${formatPrice(change)}`);
    } else {
        showNotification('Thanh toán thành công!');
    }

    closePaymentModal();
    loadCurrentOrder(); // Refresh order to show updated payment status
}

async function processMoMoPayment(orderId, amount) {
    const paymentData = {
        order_id: parseInt(orderId),
        payment_method: 'momo',
        amount: amount
    };

    const response = await fetch('/api/customer/payments/momo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData)
    });

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Lỗi khi tạo thanh toán MoMo');
    }

    const result = await response.json();

    // Redirect to MoMo payment page
    if (result.pay_url) {
        window.location.href = result.pay_url;
    } else {
        throw new Error('Không nhận được URL thanh toán từ MoMo');
    }
}
