/* Reset CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* GLOBAL ANTI-ANIMATION SETTINGS */
html {
    scroll-behavior: auto !important;
}

body {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* DISABLE ALL ANIMATIONS ON PAGE LOAD */
.no-animations,
.no-animations *,
.no-animations *::before,
.no-animations *::after {
    animation: none !important;
    animation-delay: 0s !important;
    animation-duration: 0s !important;
    animation-fill-mode: none !important;
    animation-iteration-count: 0 !important;
    animation-play-state: paused !important;
    animation-name: none !important;
    transition: none !important;
    transform: none !important;
    /* Force immediate rendering */
    will-change: auto !important;
    backface-visibility: visible !important;
    perspective: none !important;
    transform-style: flat !important;
}

/* FORCE IMMEDIATE GRID LAYOUT - NO REFLOW ANIMATIONS */
.stats-container.no-animations {
    contain: layout style !important;
    /* Prevent browser layout animations */
    overflow: visible !important;
    position: relative !important;
}

:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --gray-color: #e9ecef;
    --text-color: #2c3e50;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    color: var(--text-color);
    overflow-x: hidden;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .nav-item a span {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin-left: 0;
}

.sidebar.collapsed .logo h2 {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .logo::after {
    content: "🍽️";
    font-size: 28px;
    opacity: 1;
    visibility: visible;
    transition: var(--transition);
}

.sidebar.collapsed .nav-item {
    margin: 8px 12px;
    border-radius: 50%;
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .nav-item a {
    padding: 12px;
    justify-content: center;
    text-align: center;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .nav-item a i {
    margin-right: 0;
    font-size: 18px;
}

.sidebar.collapsed .sidebar-toggle {
    right: 50%;
    transform: translateY(-50%) translateX(50%);
}

.sidebar.collapsed .nav-item:hover {
    transform: translateX(0) scale(1.1);
}

.sidebar.collapsed .nav-item.active {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.logo {
    padding: 25px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    background: linear-gradient(45deg, #fff, #ecf0f1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: var(--transition);
}

.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.nav {
    list-style: none;
    padding: 15px 0;
    margin: 0;
}

.nav-item {
    margin: 5px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: var(--border-radius);
}

.nav-item a i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
    font-size: 18px;
    transition: var(--transition);
}

.nav-item a span {
    transition: var(--transition);
    font-weight: 500;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.nav-item:hover a {
    color: white;
}

.nav-item.active {
    background: linear-gradient(135deg, var(--primary-color), #45a049);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.nav-item.active a {
    color: white;
    font-weight: 600;
}

.nav-item.active a i {
    transform: scale(1.1);
}

/* Tooltip cho sidebar collapsed */
.sidebar.collapsed .nav-item {
    position: relative;
}

.sidebar.collapsed .nav-item::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    margin-left: 10px;
    pointer-events: none;
}

.sidebar.collapsed .nav-item::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.8);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    margin-left: 4px;
    pointer-events: none;
}

.sidebar.collapsed .nav-item:hover::after,
.sidebar.collapsed .nav-item:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    min-height: 100vh;
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.header {
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--gray-color);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mobile-toggle {
    display: none;
    background: var(--primary-color);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.mobile-toggle:hover {
    background: #45a049;
    transform: scale(1.05);
}

.search-bar {
    display: flex;
    align-items: center;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: var(--transition);
}

.search-bar:focus-within {
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
    transform: translateY(-2px);
}

.search-bar input {
    padding: 12px 20px;
    border: none;
    outline: none;
    width: 350px;
    font-size: 14px;
    background: transparent;
}

.search-bar button {
    padding: 12px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.search-bar button:hover {
    background: #45a049;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.user-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 16px;
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), #45a049);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    transition: var(--transition);
}

.user-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    min-width: 150px;
    display: none;
    z-index: 1000;
}

.dropdown-menu a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: var(--text-color);
}

.dropdown-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.dropdown-menu a:hover {
    background-color: var(--light-color);
}

.dropdown:hover .dropdown-menu {
    display: block;
}

/* Page Content */
.page-content {
    padding: 30px;
    background: transparent;
}

.page {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* Removed transition to prevent animations */
}


h2 {
    margin-bottom: 20px;
    color: var(--dark-color);
}

h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

/* Dashboard - COMPLETELY DISABLE ALL ANIMATIONS INCLUDING GRID */
.stats-container {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
    transition: none !important;
    transform: none !important;
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    /* Force immediate display */
    animation-play-state: paused !important;
    animation-name: none !important;
    /* Disable CSS Grid animations */
    grid-auto-flow: row !important;
    grid-template-rows: auto !important;
    contain: layout style !important;
}

.stats-container *,
.stats-container *::before,
.stats-container *::after {
    transition: none !important;
    transform: none !important;
    animation: none !important;
    animation-delay: 0s !important;
    animation-duration: 0s !important;
    animation-fill-mode: none !important;
    animation-iteration-count: 0 !important;
    animation-play-state: paused !important;
    animation-name: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force stat cards to be immediately visible */
.stat-card.no-animations {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
    animation: none !important;
    transition: none !important;
    animation-play-state: paused !important;
    animation-name: none !important;
    /* Prevent layout shift animations */
    contain: layout style !important;
    will-change: auto !important;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex !important;
    align-items: center;
    transition: none !important;
    transform: none !important;
    animation: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
}

.stat-card * {
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 24px;
}

.stat-info h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

/* Tables */
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.table-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.table-card.empty {
    border-left: 5px solid var(--primary-color);
}

.table-card.occupied {
    border-left: 5px solid var(--warning-color);
}

.table-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.table-capacity {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.table-location {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    font-style: italic;
}

.table-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    margin-bottom: 15px;
    font-weight: bold;
}

.table-status.empty {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--primary-color);
}

.table-status.occupied {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

/* Sửa lỗi hiển thị trạng thái "Đang phục vụ" */
.table-status {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.table-actions {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.table-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Menu */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.food-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.food-image {
    height: 150px;
    background-color: var(--gray-color);
    background-size: cover;
    background-position: center;
}

.food-info {
    padding: 15px;
}

.food-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.food-category {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.food-price {
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.food-actions {
    display: flex;
    justify-content: space-between;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table th, table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--gray-color);
}

table th {
    background-color: var(--light-color);
    font-weight: bold;
}

table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Kitchen */
.kitchen-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-pill {
    padding: 10px 20px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    font-weight: bold;
}

.stat-pill .label {
    margin-right: 10px;
}

.stat-pill .value {
    font-size: 18px;
}

.stat-pill.waiting {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

.stat-pill.cooking {
    background-color: rgba(33, 150, 243, 0.2);
    color: var(--secondary-color);
}

.stat-pill.completed {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--primary-color);
}

.kitchen-queue {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.queue-column {
    background-color: var(--light-color);
    border-radius: 8px;
    padding: 15px;
}

.queue-items {
    min-height: 400px;
}

.queue-item,
.kitchen-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 15px;
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

.kitchen-item.high-priority {
    border-left: 4px solid var(--danger-color);
    background-color: rgba(244, 67, 54, 0.05);
}

.kitchen-item.medium-priority {
    border-left: 4px solid var(--warning-color);
    background-color: rgba(255, 152, 0, 0.05);
}

.kitchen-item.normal-priority {
    border-left: 4px solid var(--primary-color);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.item-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    color: var(--dark-color);
}

.item-quantity {
    background-color: var(--secondary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
}

.item-table,
.item-time,
.item-order {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.item-table i,
.item-time i,
.item-order i {
    width: 16px;
    color: var(--secondary-color);
}

.item-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 10px;
}

.item-notes {
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(255, 193, 7, 0.1);
    border-radius: 4px;
    font-size: 14px;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 5px;
}

.item-notes i {
    color: #ffc107;
}

.empty-queue {
    padding: 40px 20px;
    text-align: center;
    color: #999;
    font-style: italic;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    border: 2px dashed #ddd;
}



/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}


.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.btn.primary {
    background: linear-gradient(135deg, var(--primary-color), #45a049);
    color: white;
}

.btn.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #1976D2);
    color: white;
}

.btn.warning {
    background: linear-gradient(135deg, var(--warning-color), #F57C00);
    color: white;
}

.btn.danger {
    background: linear-gradient(135deg, var(--danger-color), #D32F2F);
    color: white;
}

.btn.success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn.small {
    padding: 8px 16px;
    font-size: 12px;
}

/* Action Bar */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.search-filter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-box {
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 8px 15px;
    border: 1px solid var(--gray-color);
    border-radius: 4px 0 0 4px;
    width: 200px;
}

.search-box button {
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.filter {
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter label {
    color: #666;
}

.filter select {
    padding: 8px 15px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filter input {
    padding: 8px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
}

/* Table Info */
.table-info {
    margin-bottom: 20px;
    background-color: var(--light-color);
    border-radius: 8px;
    padding: 15px;
}

.table-info-row {
    display: flex;
    margin-bottom: 10px;
}

.table-info-row:last-child {
    margin-bottom: 0;
}

.table-info-label {
    font-weight: bold;
    width: 120px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.modal h2 {
    margin-bottom: 20px;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: var(--danger-color);
}

/* Categories List */
.categories-list {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
    padding: 10px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--gray-color);
}

.category-item:last-child {
    border-bottom: none;
}

.category-name {
    font-weight: bold;
}

.category-actions {
    display: flex;
    gap: 5px;
}

/* Status Badges */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
}

.status.pending {
    background-color: rgba(255, 152, 0, 0.2);
    color: var(--warning-color);
}

.status.completed {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
}

/* Image Upload */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-preview {
    width: 100%;
    height: 200px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.form-actions {
    margin-top: 20px;
    text-align: right;
}

/* Order Detail Modal */
.order-info {
    margin-bottom: 20px;
    background-color: var(--light-color);
    border-radius: 8px;
    padding: 15px;
}

.order-info-row {
    display: flex;
    margin-bottom: 10px;
}

.order-info-row:last-child {
    margin-bottom: 0;
}

.order-info-label {
    font-weight: bold;
    width: 120px;
}

.order-total {
    margin-top: 20px;
    background-color: var(--light-color);
    border-radius: 8px;
    padding: 15px;
}

.order-total-row {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 18px;
}

.order-total-label {
    color: var(--dark-color);
}

.order-total-value {
    color: var(--primary-color);
}

/* Print Preview Modal */
#print-preview-modal .modal-content {
    max-width: 800px;
}

.print-preview {
    background-color: white;
    padding: 20px;
    border: 1px solid var(--gray-color);
    border-radius: 8px;
    margin-bottom: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.print-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--gray-color);
}

.print-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.print-header p {
    color: #666;
}

.print-info {
    margin-bottom: 20px;
}

.print-info-row {
    display: flex;
    margin-bottom: 5px;
}

.print-info-label {
    font-weight: bold;
    width: 120px;
}

.print-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.print-table th, .print-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid var(--gray-color);
}

.print-table th {
    background-color: var(--light-color);
}

.print-total {
    text-align: right;
    font-weight: bold;
    font-size: 18px;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--gray-color);
}

.print-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-color);
    color: #666;
}

/* QR Code Modal */
.qr-container {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background-color: var(--light-color);
    border-radius: 8px;
}

.qr-container img {
    max-width: 100%;
    height: auto;
    border: 2px solid #333;
    border-radius: 8px;
    background-color: white;
    padding: 10px;
}

.qr-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #e8f5e8;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.qr-info p {
    margin-bottom: 10px;
}

.qr-info p:last-child {
    margin-bottom: 0;
}

.qr-info a {
    color: var(--secondary-color);
    text-decoration: none;
    word-break: break-all;
}

.qr-info a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .search-bar input {
        width: 250px;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1001;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-toggle {
        display: flex !important;
    }

    .search-bar {
        display: none;
    }

    .page-content {
        padding: 20px 15px;
    }

    .header {
        padding: 0 15px;
        height: 60px;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tables-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .kitchen-queue {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .action-bar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .action-buttons {
        justify-content: center;
    }

    .search-filter {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .user-name {
        display: none;
    }

    .page {
        padding: 20px 15px;
        margin-bottom: 15px;
    }

    .tables-grid {
        grid-template-columns: 1fr;
    }

    .menu-grid {
        grid-template-columns: 1fr;
    }

    .btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .modal-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }

    .stat-card {
        padding: 15px;
    }

    .table-card {
        padding: 15px;
    }
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.sidebar-overlay.active {
    display: block;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}

/* Enhanced Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    margin-bottom: 12px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    animation: slideInNotification 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    border-left: 4px solid #4CAF50;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}


.notification.success { border-left-color: #4CAF50; }
.notification.error { border-left-color: #f44336; }
.notification.warning { border-left-color: #ff9800; }
.notification.info { border-left-color: #2196F3; }

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification.success .notification-icon {
    color: white;
    background: #4CAF50;
}
.notification.error .notification-icon {
    color: white;
    background: #f44336;
}
.notification.warning .notification-icon {
    color: white;
    background: #ff9800;
}
.notification.info .notification-icon {
    color: white;
    background: #2196F3;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
}

.notification-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    color: #999;
    cursor: pointer;
    padding: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: #f0f0f0;
    color: #666;
    transform: scale(1.1);
}

@keyframes slideInNotification {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutNotification {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Removed shimmer keyframes */

/* Enhanced Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 20px;
    font-size: 16px;
    color: #666;
    text-align: center;
}

/* Enhanced Button States */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* Removed ripple effect from buttons */

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

/* Enhanced Card Animations */
.table-card, .food-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}



.table-card:hover, .food-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}


/* Removed page transition animations */
.page {
    opacity: 1;
    transform: none;
}

.page[style*="block"] {
    opacity: 1;
    transform: none;
}

/* Enhanced Modal Animations */
.modal {
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.modal[style*="flex"] {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s ease;
}

.modal[style*="flex"] .modal-content {
    transform: scale(1) translateY(0);
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}


/* Enhanced Status Badges */
.status-badge {
    position: relative;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.fab:active {
    transform: scale(0.95);
}

/* Tooltip */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Key status styles */
.key-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    margin: 4px 0;
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
}

.key-status.active {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

.key-status.inactive {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.key-status small {
    font-size: 0.9em;
    opacity: 0.8;
    display: block;
    margin-top: 2px;
}

.key-status i {
    font-size: 0.9em;
}
