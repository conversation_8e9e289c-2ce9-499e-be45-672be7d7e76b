/* ==================== MODERN COMPONENTS ==================== */

/* Modern Button System */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-sans);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-modern:active {
  transform: translateY(0);
  transition: transform var(--transition-fast);
}

.btn-modern:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.btn-warning {
  background: var(--gradient-warning);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.btn-error {
  background: var(--gradient-error);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-error:hover {
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

/* Button Sizes */
.btn-xs {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--text-xl);
  border-radius: var(--radius-2xl);
}

.btn-full {
  width: 100%;
}

/* Modern Card System */
.card-modern {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
}

.card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-base);
}

.card-modern:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  border-color: var(--primary-200);
}

.card-modern:hover::before {
  transform: scaleX(1);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.card-compact .card-header,
.card-compact .card-body,
.card-compact .card-footer {
  padding: var(--space-4);
}

/* Stats Cards */
.stats-card {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stats-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-300);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  color: white;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
  position: relative;
}

.stats-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
}

.stats-card:hover .stats-icon {
  transform: scale(1.1) rotate(5deg);
}

.stats-icon.success {
  background: var(--gradient-success);
}

.stats-icon.warning {
  background: var(--gradient-warning);
}

.stats-icon.error {
  background: var(--gradient-error);
}

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-600);
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
  line-height: var(--leading-none);
  margin-bottom: var(--space-2);
}

.stats-change {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  width: fit-content;
}

.stats-change.positive {
  color: var(--success-700);
  background-color: var(--success-100);
}

.stats-change.negative {
  color: var(--error-700);
  background-color: var(--error-100);
}

.stats-change.neutral {
  color: var(--gray-700);
  background-color: var(--gray-100);
}

/* Modern Form Elements */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  display: block;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--gray-900);
  background-color: white;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100), var(--shadow-md);
  transform: translateY(-1px);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-error {
  color: var(--error-600);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-help {
  color: var(--gray-500);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* Modern Badge System */
.badge-modern {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.badge-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
}

.badge-modern:hover::before {
  left: 100%;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-800);
  border: 1px solid var(--primary-200);
}

.badge-success {
  background: var(--success-100);
  color: var(--success-800);
  border: 1px solid var(--success-200);
}

.badge-warning {
  background: var(--warning-100);
  color: var(--warning-800);
  border: 1px solid var(--warning-200);
}

.badge-error {
  background: var(--error-100);
  color: var(--error-800);
  border: 1px solid var(--error-200);
}

.badge-gray {
  background: var(--gray-100);
  color: var(--gray-800);
  border: 1px solid var(--gray-200);
}

/* Modern Table */
.table-modern {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.table-modern thead {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
}

.table-modern th {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--gray-200);
  position: relative;
}

.table-modern th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.table-modern th:hover::after {
  transform: scaleX(1);
}

.table-modern td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-700);
  font-size: var(--text-sm);
  transition: all var(--transition-base);
}

.table-modern tbody tr {
  transition: all var(--transition-base);
}

.table-modern tbody tr:hover {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-25) 100%);
  transform: scale(1.01);
}

.table-modern tbody tr:hover td {
  color: var(--gray-900);
}

.table-modern tbody tr:last-child td {
  border-bottom: none;
}
