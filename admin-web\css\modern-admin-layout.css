/* ==================== MODERN ADMIN LAYOUT ==================== */

/* Main App Layout */
.modern-app {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  position: relative;
}

/* Modern Sidebar */
.modern-sidebar {
  width: var(--sidebar-width);
  background: white;
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
  transform: translateX(0);
}

.modern-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

.modern-sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.modern-sidebar.mobile-hidden {
  transform: translateX(-100%);
}

/* Sidebar Header */
.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
  position: relative;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-base);
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  color: white;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
}

.logo-icon:hover::before {
  left: 100%;
}

.logo-icon:hover {
  transform: scale(1.05) rotate(5deg);
}

.logo-text {
  font-family: var(--font-display);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
  transition: opacity var(--transition-base);
}

.modern-sidebar.collapsed .logo-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: var(--space-4) 0;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  padding: 0 var(--space-6) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: opacity var(--transition-base);
}

.modern-sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  margin: var(--space-1) var(--space-3);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-primary);
  transform: scaleY(0);
  transition: transform var(--transition-base);
  border-radius: 0 var(--radius-base) var(--radius-base) 0;
}

.nav-item:hover {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-25) 100%);
  color: var(--primary-700);
  transform: translateX(4px);
}

.nav-item:hover::before {
  transform: scaleY(1);
}

.nav-item.active {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item.active::before {
  transform: scaleY(1);
  background: rgba(255, 255, 255, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--space-3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  transition: all var(--transition-base);
}

.nav-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: opacity var(--transition-base);
}

.modern-sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.modern-sidebar.collapsed .nav-item {
  justify-content: center;
  margin: var(--space-1) var(--space-2);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.expanded {
  margin-left: var(--sidebar-width-collapsed);
}

/* Modern Header */
.modern-header {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--space-6);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  width: 44px;
  height: 44px;
  background: var(--gray-100);
  border: none;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-base);
}

.sidebar-toggle:hover {
  background: var(--primary-100);
  color: var(--primary-600);
  transform: scale(1.05);
}

.page-title {
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.action-btn {
  width: 44px;
  height: 44px;
  background: var(--gray-100);
  border: none;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.action-btn:hover {
  background: var(--primary-100);
  color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  cursor: pointer;
  background: var(--gray-50);
}

.user-menu:hover {
  background: var(--primary-50);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.user-menu:hover .user-avatar {
  transform: scale(1.05);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.user-role {
  font-size: var(--text-xs);
  color: var(--gray-500);
  line-height: var(--leading-tight);
}

/* Content Area */
.content-area {
  flex: 1;
  padding: var(--space-8);
  overflow-y: auto;
  background: transparent;
}

.page {
  display: none;
  animation: fadeIn var(--transition-base) var(--ease-out);
}

.page.active {
  display: block;
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-8);
  text-align: center;
}

.page-header h1 {
  font-family: var(--font-display);
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--space-3);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: var(--text-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .modern-sidebar {
    transform: translateX(-100%);
  }
  
  .modern-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .content-area {
    padding: var(--space-4);
  }
  
  .modern-header {
    padding: 0 var(--space-4);
  }
  
  .page-title {
    font-size: var(--text-xl);
  }
  
  .user-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .page-header h1 {
    font-size: var(--text-3xl);
  }
  
  .page-header p {
    font-size: var(--text-base);
  }
  
  .header-actions {
    gap: var(--space-1);
  }
  
  .action-btn {
    width: 40px;
    height: 40px;
  }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  backdrop-filter: blur(4px);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}
