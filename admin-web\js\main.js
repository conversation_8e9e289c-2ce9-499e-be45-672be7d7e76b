// <PERSON><PERSON><PERSON><PERSON> toàn cục
const API_URL = 'http://localhost:3000/api';

// Enhanced Notification System
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
        this.notifications = new Map();
        this.nextId = 1;
    }

    createContainer() {
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, type = 'info', title = null, duration = 5000) {
        const id = this.nextId++;
        const notification = this.createNotification(id, message, type, title);

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => this.remove(id), duration);
        }

        return id;
    }

    createNotification(id, message, type, title) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.dataset.id = id;

        const iconMap = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info'
        };

        const titleMap = {
            success: title || 'Thành công',
            error: title || 'Lỗi',
            warning: title || 'Cảnh báo',
            info: title || 'Thông tin'
        };

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${titleMap[type]}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.remove(${id})">
                <i class="fas fa-times"></i>
            </button>
        `;

        return notification;
    }

    remove(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.style.animation = 'slideOutNotification 0.3s ease forwards';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    success(message, title = null) {
        return this.show(message, 'success', title);
    }

    error(message, title = null) {
        return this.show(message, 'error', title);
    }

    warning(message, title = null) {
        return this.show(message, 'warning', title);
    }

    info(message, title = null) {
        return this.show(message, 'info', title);
    }
}

// Initialize notification manager
const notificationManager = new NotificationManager();

// Loading State Manager
class LoadingManager {
    constructor() {
        this.loadingElements = new Set();
    }

    show(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.add('loading');
            this.loadingElements.add(element);
        }
    }

    hide(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.remove('loading');
            this.loadingElements.delete(element);
        }
    }

    hideAll() {
        this.loadingElements.forEach(element => {
            element.classList.remove('loading');
        });
        this.loadingElements.clear();
    }
}

// Initialize loading manager
const loadingManager = new LoadingManager();

// Hàm tải lên hình ảnh
async function uploadImage(file, type = 'foods') {
    try {
        console.log(`Đang tải lên hình ảnh ${type}...`, file.name, file.type, file.size);

        // Tạo form data
        const formData = new FormData();
        formData.append('image', file);

        // Tải lên hình ảnh thông qua API Gateway
        const response = await fetch(`${API_URL}/images/${type}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });

        console.log('Phản hồi từ server:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Nội dung lỗi:', errorText);
            throw new Error(errorText || 'Không thể tải lên hình ảnh');
        }

        const data = await response.json();
        console.log('Hình ảnh đã được tải lên:', data);
        return data.url;
    } catch (error) {
        console.error(`Lỗi khi tải lên hình ảnh ${type}:`, error);
        throw error;
    }
}

let token = localStorage.getItem('token');
let currentUser = null;

// Kiểm tra đăng nhập khi tải trang
document.addEventListener('DOMContentLoaded', () => {
    // Kiểm tra token
    if (!token) {
        showLoginModal();
    } else {
        // Lấy thông tin người dùng
        fetchCurrentUser();
    }

    // Xử lý sự kiện đăng nhập
    document.getElementById('login-form').addEventListener('submit', handleLogin);

    // Xử lý sự kiện đăng xuất
    document.getElementById('logout-btn').addEventListener('click', handleLogout);

    // Xử lý chuyển đổi trang
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault(); // Ngăn reload trang
            const pageId = item.getAttribute('data-page');
            changePage(pageId);
        });
    });

    // Xử lý sidebar toggle
    initSidebarToggle();

    // Tải dữ liệu trang Dashboard mặc định
    loadDashboardData();

    // Setup auto-refresh for different pages
    setupAutoRefresh();

    // Add keyboard shortcuts
    setupKeyboardShortcuts();

    // Removed welcome notification to reduce distractions
});

// Khởi tạo sidebar toggle
function initSidebarToggle() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileToggle = document.getElementById('mobile-toggle');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    // Desktop sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', (e) => {
            e.preventDefault();
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Lưu trạng thái sidebar
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
    }

    // Mobile sidebar toggle
    if (mobileToggle) {
        mobileToggle.addEventListener('click', (e) => {
            e.preventDefault();
            sidebar.classList.toggle('mobile-open');
            sidebarOverlay.classList.toggle('active');
        });
    }

    // Đóng sidebar khi click overlay
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', () => {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
        });
    }

    // Khôi phục trạng thái sidebar từ localStorage
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }

    // Xử lý responsive
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('mobile-open');
            sidebarOverlay.classList.remove('active');
        }
    });
}

// Hiển thị modal đăng nhập
function showLoginModal() {
    const modal = document.getElementById('login-modal');
    modal.style.display = 'flex';
}

// Ẩn modal đăng nhập
function hideLoginModal() {
    const modal = document.getElementById('login-modal');
    modal.style.display = 'none';
}

// Xử lý đăng nhập
async function handleLogin(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    console.log('Đang đăng nhập với:', { username, password });

    try {
        console.log('Đang đăng nhập với:', { username, password });

        const response = await fetch(`${API_URL}/users/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        console.log('Phản hồi từ server:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Lỗi đăng nhập:', errorText);
            throw new Error('Đăng nhập thất bại: ' + errorText);
        }

        const data = await response.json();
        console.log('Dữ liệu đăng nhập:', data);

        token = data.token;
        currentUser = data.user;

        // Lưu token vào localStorage
        localStorage.setItem('token', token);

        // Cập nhật UI
        document.querySelector('.user-name').textContent = currentUser.username;

        // Ẩn modal đăng nhập
        hideLoginModal();

        // Tải dữ liệu trang Dashboard
        loadDashboardData();
    } catch (error) {
        console.error('Lỗi xử lý đăng nhập:', error);
        notificationManager.error('Đăng nhập thất bại: ' + error.message);
    }
}

// Xử lý đăng xuất
function handleLogout(event) {
    event.preventDefault();

    // Xóa token và thông tin người dùng
    localStorage.removeItem('token');
    token = null;
    currentUser = null;

    // Hiển thị modal đăng nhập
    showLoginModal();
}

// Lấy thông tin người dùng hiện tại
async function fetchCurrentUser() {
    try {
        console.log('Đang lấy thông tin người dùng với token:', token);

        const response = await fetch(`${API_URL}/users/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('Phản hồi từ server:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Lỗi khi lấy thông tin người dùng:', errorText);
            throw new Error('Không thể lấy thông tin người dùng: ' + errorText);
        }

        currentUser = await response.json();
        console.log('Thông tin người dùng:', currentUser);

        document.querySelector('.user-name').textContent = currentUser.username;
    } catch (error) {
        console.error('Lỗi khi lấy thông tin người dùng:', error);
        // Token không hợp lệ hoặc hết hạn
        localStorage.removeItem('token');
        token = null;
        showLoginModal();
    }
}

// Biến lưu trữ interval ID cho việc làm mới dữ liệu tự động
let autoRefreshInterval = null;

// Chuyển đổi trang
function changePage(pageId) {
    // Ẩn tất cả các trang
    document.querySelectorAll('.page').forEach(page => {
        page.style.display = 'none';
    });

    // Hiển thị trang được chọn
    document.getElementById(pageId).style.display = 'block';

    // Cập nhật trạng thái active cho menu
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`.nav-item[data-page="${pageId}"]`).classList.add('active');

    // Xóa interval làm mới dữ liệu tự động nếu có
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }

    // Tải dữ liệu cho trang tương ứng
    switch (pageId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'tables':
            loadTablesData();
            break;
        case 'menu':
            loadMenuData();
            break;
        case 'orders':
            loadOrdersData();
            break;
        case 'kitchen':
            loadKitchenData();
            // Thiết lập làm mới dữ liệu tự động cho trang nhà bếp
            autoRefreshInterval = setInterval(() => {
                loadKitchenData();
            }, 30000); // Làm mới mỗi 30 giây
            break;
        case 'inventory':
            loadInventoryData();
            break;
        case 'users':
            loadUsersData();
            break;
    }
}

// Tải dữ liệu cho trang Dashboard
async function loadDashboardData() {
    if (!token) return;

    // Show loading state
    loadingManager.show('.stats-container');
    loadingManager.show('.detailed-stats');
    loadingManager.show('#recent-orders-table');

    try {
        // Lấy số lượng bàn đang phục vụ
        const tablesResponse = await fetch(`${API_URL}/tables`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (tablesResponse.ok) {
            const tables = await tablesResponse.json();
            console.log('Tables data:', tables);

            // Kiểm tra các trạng thái có thể có
            const activeTables = tables.filter(table =>
                table.status === 'Đang phục vụ' ||
                table.status === 'Occupied' ||
                table.status === 'occupied'
            ).length;

            document.getElementById('active-tables').textContent = activeTables;
        } else {
            console.error('Failed to fetch tables:', tablesResponse.status);
            document.getElementById('active-tables').textContent = 'N/A';
        }

        // Lấy số lượng đơn hàng hôm nay
        const today = new Date().toISOString().split('T')[0];
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowStr = tomorrow.toISOString().split('T')[0];

        const ordersResponse = await fetch(`${API_URL}/orders?from_date=${today}&to_date=${tomorrowStr}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (ordersResponse.ok) {
            const orders = await ordersResponse.json();
            console.log('Today orders data:', orders);
            document.getElementById('today-orders').textContent = orders.length || 0;

            // Hiển thị đơn hàng gần đây
            const recentOrdersTable = document.getElementById('recent-orders-table');
            recentOrdersTable.innerHTML = '';

            orders.slice(0, 5).forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.id}</td>
                    <td>${order.table_name}</td>
                    <td>${new Date(order.order_time).toLocaleString()}</td>
                    <td>${formatCurrency(order.total)}</td>
                    <td><span class="status ${order.status === 'Đã thanh toán' ? 'completed' : 'pending'}">${order.status}</span></td>
                `;
                recentOrdersTable.appendChild(row);
            });
        } else {
            console.error('Failed to fetch today orders:', ordersResponse.status);
            document.getElementById('today-orders').textContent = 'N/A';
        }

        // Lấy số lượng món ăn đang chế biến
        const kitchenResponse = await fetch(`${API_URL}/kitchen/stats`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (kitchenResponse.ok) {
            const kitchenStats = await kitchenResponse.json();
            document.getElementById('cooking-items').textContent = kitchenStats.cooking_count || 0;
        } else {
            console.error('Failed to fetch kitchen stats:', kitchenResponse.status);
            document.getElementById('cooking-items').textContent = 'N/A';
        }

        // Lấy số lượng nguyên liệu sắp hết
        const inventoryResponse = await fetch(`${API_URL}/inventory/low-stock/list`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (inventoryResponse.ok) {
            const lowStock = await inventoryResponse.json();
            document.getElementById('low-inventory').textContent = lowStock.length || 0;
        } else {
            console.error('Failed to fetch inventory:', inventoryResponse.status);
            document.getElementById('low-inventory').textContent = 'N/A';
        }

        // Lấy tổng số người dùng
        const usersResponse = await fetch(`${API_URL}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (usersResponse.ok) {
            const users = await usersResponse.json();
            document.getElementById('total-users').textContent = users.length || 0;
        } else {
            console.error('Failed to fetch users:', usersResponse.status);
            document.getElementById('total-users').textContent = 'N/A';
        }

        // Lấy tổng số món ăn
        const foodsResponse = await fetch(`${API_URL}/menu/foods`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (foodsResponse.ok) {
            const foods = await foodsResponse.json();
            document.getElementById('total-foods').textContent = foods.length || 0;
        } else {
            console.error('Failed to fetch foods:', foodsResponse.status);
            document.getElementById('total-foods').textContent = 'N/A';
        }

        // Tính số bàn trống và doanh thu hôm nay
        if (tablesResponse.ok) {
            const tables = await tablesResponse.json();
            console.log('Tables for empty count:', tables.map(t => ({ name: t.name, status: t.status })));

            // Kiểm tra các trạng thái trống có thể có
            const emptyTables = tables.filter(table =>
                table.status === 'Trống' ||
                table.status === 'Available' ||
                table.status === 'Empty' ||
                table.status === 'available' ||
                table.status === 'empty'
            ).length;

            console.log('Empty tables count:', emptyTables);
            document.getElementById('empty-tables').textContent = emptyTables;
        } else {
            document.getElementById('empty-tables').textContent = 'N/A';
        }

        // Tính doanh thu hôm nay từ các đơn hàng đã thanh toán
        if (ordersResponse.ok) {
            const orders = await ordersResponse.json();
            const paidOrders = orders.filter(order => order.status === 'Đã thanh toán');
            const todayRevenue = paidOrders.reduce((total, order) => total + (order.total || 0), 0);
            document.getElementById('today-revenue').textContent = formatCurrency(todayRevenue);

            // Doanh thu hôm nay đã được cập nhật
        }

        // Lấy tổng số đơn hàng (tất cả thời gian)
        const allOrdersResponse = await fetch(`${API_URL}/orders`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (allOrdersResponse.ok) {
            const allOrders = await allOrdersResponse.json();
            const allPaidOrders = allOrders.filter(order => order.status === 'Đã thanh toán');

            document.getElementById('total-orders').textContent = allOrders.length || 0;
            document.getElementById('paid-orders').textContent = allPaidOrders.length || 0;
        } else {
            console.error('Failed to fetch all orders:', allOrdersResponse.status);
            document.getElementById('total-orders').textContent = 'N/A';
            document.getElementById('paid-orders').textContent = 'N/A';
        }

        // Cập nhật thống kê chi tiết cho bàn
        if (tablesResponse.ok) {
            const tables = await tablesResponse.json();
            const totalTables = tables.length;
            const activeTables = tables.filter(table => table.status === 'Đang phục vụ').length;
            const usageRate = totalTables > 0 ? Math.round((activeTables / totalTables) * 100) : 0;

            document.getElementById('total-tables').textContent = totalTables;
            document.getElementById('table-usage-rate').textContent = `${usageRate}%`;
        }

        // Lấy thống kê storage (chỉ admin mới có quyền)
        try {
            const storageResponse = await fetch(`${API_URL}/images/storage/stats`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (storageResponse.ok) {
                const storageStats = await storageResponse.json();
                console.log('Storage stats:', storageStats);
                document.getElementById('food-images-count').textContent = `${storageStats.foods.total} files`;
                document.getElementById('food-images-size').textContent = `${storageStats.foods.sizeMB} MB`;
                document.getElementById('user-images-count').textContent = `${storageStats.users.total} files`;
                document.getElementById('temp-files-count').textContent = `${storageStats.temp.total} files`;
            } else {
                // Nếu không có quyền truy cập storage stats, hiển thị N/A
                document.getElementById('food-images-count').textContent = 'N/A';
                document.getElementById('food-images-size').textContent = 'N/A';
                document.getElementById('user-images-count').textContent = 'N/A';
                document.getElementById('temp-files-count').textContent = 'N/A';
            }
        } catch (error) {
            console.log('Không thể lấy thống kê storage (có thể do quyền hạn)');
            document.getElementById('food-images-count').textContent = 'N/A';
            document.getElementById('food-images-size').textContent = 'N/A';
            document.getElementById('user-images-count').textContent = 'N/A';
            document.getElementById('temp-files-count').textContent = 'N/A';
        }
    } catch (error) {
        console.error('Lỗi khi tải dữ liệu Dashboard:', error);
        notificationManager.error('Lỗi khi tải dữ liệu Dashboard');
    } finally {
        // Hide loading state
        loadingManager.hide('.stats-container');
        loadingManager.hide('.detailed-stats');
        loadingManager.hide('#recent-orders-table');
    }
}

// Biến lưu trữ danh sách bàn
let tables = [];

// Tải dữ liệu cho trang Tables
async function loadTablesData() {
    if (!token) return;

    try {
        const response = await fetch(`${API_URL}/tables`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách bàn');
        }

        tables = await response.json();
        console.log('Danh sách bàn:', tables);

        // Hiển thị danh sách bàn
        displayTables(tables);

        // Thêm sự kiện cho nút thêm bàn mới
        document.getElementById('add-table-btn').addEventListener('click', addTable);

        // Thêm sự kiện cho ô tìm kiếm với debounce
        const debouncedFilter = utils.debounce(filterTables, 300);
        document.getElementById('table-search').addEventListener('input', debouncedFilter);
        document.getElementById('table-search-btn').addEventListener('click', filterTables);

        // Thêm sự kiện cho bộ lọc trạng thái và vị trí
        document.getElementById('table-status-filter').addEventListener('change', filterTables);
        document.getElementById('table-location-filter').addEventListener('change', filterTables);

    } catch (error) {
        console.error('Lỗi khi tải dữ liệu bàn:', error);
        alert('Lỗi khi tải dữ liệu bàn: ' + error.message);
    }
}

// Thêm bàn mới
function addTable() {
    // Reset form
    document.getElementById('table-form').reset();
    document.getElementById('table-id').value = '';
    document.getElementById('table-modal-title').textContent = 'Thêm bàn mới';

    // Hiển thị modal
    const modal = document.getElementById('table-modal');
    modal.style.display = 'flex';

    // Thêm sự kiện cho nút đóng
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.onclick = () => {
        modal.style.display = 'none';
    };

    // Thêm sự kiện cho nút hủy
    const cancelBtn = document.getElementById('cancel-table-btn');
    cancelBtn.onclick = () => {
        modal.style.display = 'none';
    };

    // Thêm sự kiện submit form
    const form = document.getElementById('table-form');
    form.onsubmit = async (e) => {
        e.preventDefault();

        // Lấy dữ liệu từ form
        const tableData = {
            name: document.getElementById('table-name').value,
            capacity: parseInt(document.getElementById('table-capacity').value),
            status: document.getElementById('table-status').value,
            location: document.getElementById('table-location').value,
            note: document.getElementById('table-note').value
        };

        try {
            // Gọi API để thêm bàn mới
            const response = await fetch(`${API_URL}/tables`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tableData)
            });

            if (!response.ok) {
                throw new Error('Không thể thêm bàn mới');
            }

            // Đóng modal
            modal.style.display = 'none';

            // Tải lại danh sách bàn
            await loadTablesData();

            // Hiển thị thông báo thành công
            notificationManager.success('Thêm bàn mới thành công');

        } catch (error) {
            console.error('Lỗi khi thêm bàn mới:', error);
            notificationManager.error('Lỗi khi thêm bàn mới: ' + error.message);
        }
    };
}

// Hiển thị danh sách bàn
function displayTables(tablesToDisplay) {
    const tablesContainer = document.getElementById('tables-container');
    tablesContainer.innerHTML = '';

    if (!tablesToDisplay || tablesToDisplay.length === 0) {
        tablesContainer.innerHTML = `
            <div class="empty-message">
                <i class="fas fa-table"></i>
                <h3>Không có bàn nào</h3>
                <p>Hãy thêm bàn mới để hiển thị tại đây</p>
                <button class="btn primary" id="add-empty-table-btn">
                    <i class="fas fa-plus"></i> Thêm bàn mới
                </button>
            </div>
        `;

        // Thêm sự kiện cho nút thêm bàn khi không có bàn nào
        document.getElementById('add-empty-table-btn').addEventListener('click', addTable);
        return;
    }

    tablesToDisplay.forEach(table => {
        const tableCard = document.createElement('div');
        tableCard.className = `table-card ${table.status === 'Trống' ? 'empty' : 'occupied'}`;
        tableCard.dataset.id = table.id;

        // Thêm thông tin bàn
        const capacity = table.capacity || 4;
        const location = table.location || 'Tầng 1';

        tableCard.innerHTML = `
            <div class="table-number">${table.name}</div>
            <div class="table-capacity"><i class="fas fa-users"></i> ${capacity} người</div>
            <div class="table-location"><i class="fas fa-map-marker-alt"></i> ${location}</div>
            <div class="table-status ${table.status === 'Trống' ? 'empty' : 'occupied'}">${table.status}</div>
            <div class="table-actions">
                ${table.status === 'Trống' ?
                    `<button class="btn primary generate-qr-btn" data-id="${table.id}"><i class="fas fa-qrcode"></i> Tạo QR</button>` :
                    `<button class="btn warning view-order-btn" data-id="${table.id}"><i class="fas fa-eye"></i> Xem đơn</button>`
                }
                <button class="btn secondary view-table-btn" data-id="${table.id}"><i class="fas fa-info-circle"></i></button>
            </div>
            ${table.status === 'Đang phục vụ' && table.current_order ?
                `<div class="table-badge" title="Số món ăn">${table.current_order.item_count || 0}</div>` :
                ''
            }
        `;

        tablesContainer.appendChild(tableCard);

        // Thêm sự kiện click cho card bàn
        tableCard.addEventListener('click', (e) => {
            // Chỉ xử lý click vào card, không xử lý click vào nút
            if (!e.target.closest('button')) {
                viewTableDetail(table.id);
            }
        });
    });

    // Thêm sự kiện cho các nút
    document.querySelectorAll('.generate-qr-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation(); // Ngăn sự kiện click lan ra card
            const tableId = btn.getAttribute('data-id');
            generateQRCode(tableId);
        });
    });

    document.querySelectorAll('.view-order-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation(); // Ngăn sự kiện click lan ra card
            const tableId = btn.getAttribute('data-id');
            viewTableOrder(tableId);
        });
    });

    document.querySelectorAll('.view-table-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation(); // Ngăn sự kiện click lan ra card
            const tableId = btn.getAttribute('data-id');
            viewTableDetail(tableId);
        });
    });
}

// Lọc danh sách bàn
function filterTables() {
    const searchText = document.getElementById('table-search').value.toLowerCase();
    const statusFilter = document.getElementById('table-status-filter').value;
    const locationFilter = document.getElementById('table-location-filter').value;

    console.log('Lọc bàn:', { searchText, statusFilter, locationFilter });

    // Lọc bàn theo các điều kiện
    const filteredTables = tables.filter(table => {
        // Lọc theo tên bàn
        const nameMatch = table.name.toLowerCase().includes(searchText);

        // Lọc theo trạng thái
        const statusMatch = statusFilter === 'all' || table.status === statusFilter;

        // Lọc theo vị trí
        const locationMatch = locationFilter === 'all' || table.location === locationFilter;

        return nameMatch && statusMatch && locationMatch;
    });

    // Hiển thị danh sách bàn đã lọc
    displayTables(filteredTables);
}

// Tạo QR code cho bàn với kiểm tra trạng thái key (hàm cũ - sẽ được thay thế)
async function generateQRCodeOld(tableId) {
    try {
        const response = await fetch(`${API_URL}/tables/${tableId}/qrcode`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể tạo QR code');
        }

        const data = await response.json();

        // Hiển thị QR code trong modal
        alert(`QR code đã được tạo thành công! URL: ${data.url}`);

        // Tải lại dữ liệu bàn
        loadTablesData();
    } catch (error) {
        console.error('Lỗi khi tạo QR code:', error);
        alert('Lỗi khi tạo QR code: ' + error.message);
    }
}

// Định dạng tiền tệ (sử dụng utility)
function formatCurrency(amount) {
    return utils.formatCurrency(amount);
}

// Các hàm bổ sung cho các chức năng

// Biến lưu trữ danh sách danh mục
let categories = [];
// Biến lưu trữ danh sách món ăn
let foods = [];

// Hàm chỉnh sửa món ăn
async function editFood(foodId) {
    console.log('Chỉnh sửa món ăn có ID:', foodId);

    try {
        // Lấy thông tin món ăn
        const response = await fetch(`${API_URL}/menu/foods/${foodId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy thông tin món ăn');
        }

        const food = await response.json();
        console.log('Thông tin món ăn:', food);

        // Hiển thị modal chỉnh sửa món ăn
        document.getElementById('food-modal-title').textContent = 'Chỉnh sửa món ăn';
        document.getElementById('food-id').value = food.id;
        document.getElementById('food-name').value = food.name;
        document.getElementById('food-category').value = food.category_id;
        document.getElementById('food-price').value = food.price;
        document.getElementById('food-description').value = food.description || '';
        document.getElementById('food-image').value = food.image_url || '';

        // Hiển thị hình ảnh nếu có
        const previewImg = document.getElementById('food-image-preview-img');
        if (food.image_url) {
            previewImg.src = food.image_url;
        } else {
            previewImg.src = 'img/default-food.jpg';
        }

        // Hiển thị modal
        const modal = document.getElementById('food-modal');
        modal.style.display = 'flex';

    } catch (error) {
        console.error('Lỗi khi lấy thông tin món ăn:', error);
        alert('Lỗi khi lấy thông tin món ăn: ' + error.message);
    }
}

// Hàm xóa món ăn
function deleteFood(foodId, forceDelete = false) {
    console.log('Xóa món ăn có ID:', foodId, 'Force delete:', forceDelete);

    // Tìm món ăn trong danh sách
    const food = foods.find(f => f.id == foodId);
    if (!food) {
        alert('Không tìm thấy thông tin món ăn');
        return;
    }

    // Hiển thị modal xác nhận xóa
    const confirmModal = document.getElementById('confirm-modal');
    const confirmMessage = document.getElementById('confirm-message');

    if (forceDelete) {
        confirmMessage.textContent = 'Món ăn này đã có trong đơn hàng. Bạn có chắc chắn muốn xóa?';
    } else {
        confirmMessage.textContent = 'Bạn có chắc chắn muốn xóa món ăn này?';
    }

    // Hiển thị modal
    confirmModal.style.display = 'flex';

    // Xử lý sự kiện nút xác nhận
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.onclick = async () => {
        try {
            // Gọi API xóa món ăn
            const url = forceDelete
                ? `${API_URL}/menu/foods/${foodId}?force=true`
                : `${API_URL}/menu/foods/${foodId}`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json();

                // Kiểm tra nếu món ăn có trong đơn hàng và có thể xóa bằng force
                if (errorData.canForceDelete) {
                    // Đóng modal hiện tại
                    confirmModal.style.display = 'none';

                    // Gọi lại hàm xóa với tham số force=true
                    deleteFood(foodId, true);
                    return;
                }

                throw new Error(errorData.message || 'Không thể xóa món ăn');
            }

            // Nếu món ăn có hình ảnh, xóa hình ảnh
            if (food.image_url && food.image_url.startsWith('/api/images/foods/')) {
                // Lấy tên file từ URL
                const filename = food.image_url.split('/').pop();

                try {
                    // Gọi API xóa hình ảnh
                    await fetch(`${API_URL}/images/foods/${filename}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    console.log('Đã xóa hình ảnh:', filename);
                } catch (imageError) {
                    console.error('Lỗi khi xóa hình ảnh:', imageError);
                    // Không dừng quá trình nếu xóa hình ảnh thất bại
                }
            }

            // Đóng modal
            confirmModal.style.display = 'none';

            // Tải lại dữ liệu menu
            await loadMenuData();

            alert('Xóa món ăn thành công');

        } catch (error) {
            console.error('Lỗi khi xóa món ăn:', error);
            alert('Lỗi khi xóa món ăn: ' + error.message);

            // Đóng modal
            confirmModal.style.display = 'none';
        }
    };

    // Xử lý sự kiện nút hủy
    const cancelBtn = document.getElementById('cancel-confirm-btn');
    cancelBtn.onclick = () => {
        confirmModal.style.display = 'none';
    };
}

// Hàm thêm món ăn mới
function addFood() {
    console.log('Thêm món ăn mới');

    // Hiển thị modal thêm món ăn
    document.getElementById('food-modal-title').textContent = 'Thêm món ăn mới';
    document.getElementById('food-id').value = '';
    document.getElementById('food-name').value = '';
    document.getElementById('food-price').value = '';
    document.getElementById('food-description').value = '';
    document.getElementById('food-image').value = '';

    // Reset hình ảnh xem trước
    document.getElementById('food-image-preview-img').src = 'img/default-food.jpg';

    // Hiển thị modal
    const modal = document.getElementById('food-modal');
    modal.style.display = 'flex';
}

// Hàm quản lý danh mục
async function manageCategories() {
    console.log('Quản lý danh mục');

    try {
        // Lấy danh sách danh mục
        const response = await fetch(`${API_URL}/menu/categories`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách danh mục');
        }

        categories = await response.json();
        console.log('Danh sách danh mục:', categories);

        // Hiển thị danh sách danh mục
        const categoriesList = document.getElementById('categories-list');
        categoriesList.innerHTML = '';

        categories.forEach(category => {
            const categoryItem = document.createElement('div');
            categoryItem.className = 'category-item';
            categoryItem.innerHTML = `
                <div class="category-name">${category.name}</div>
                <div class="category-actions">
                    <button class="btn small edit-category-btn" data-id="${category.id}"><i class="fas fa-edit"></i></button>
                    <button class="btn small danger delete-category-btn" data-id="${category.id}"><i class="fas fa-trash"></i></button>
                </div>
            `;
            categoriesList.appendChild(categoryItem);
        });

        // Thêm sự kiện cho các nút
        document.querySelectorAll('.edit-category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const categoryId = btn.getAttribute('data-id');
                editCategory(categoryId);
            });
        });

        document.querySelectorAll('.delete-category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const categoryId = btn.getAttribute('data-id');
                deleteCategory(categoryId);
            });
        });

        // Reset form thêm danh mục
        document.getElementById('category-id').value = '';
        document.getElementById('category-name').value = '';
        document.getElementById('save-category-btn').textContent = 'Thêm danh mục';

        // Hiển thị modal
        const modal = document.getElementById('categories-modal');
        modal.style.display = 'flex';

    } catch (error) {
        console.error('Lỗi khi lấy danh sách danh mục:', error);
        alert('Lỗi khi lấy danh sách danh mục: ' + error.message);
    }
}

// Hàm chỉnh sửa danh mục
function editCategory(categoryId) {
    console.log('Chỉnh sửa danh mục có ID:', categoryId);

    // Tìm danh mục trong danh sách
    const category = categories.find(c => c.id == categoryId);

    if (category) {
        // Điền thông tin danh mục vào form
        document.getElementById('category-id').value = category.id;
        document.getElementById('category-name').value = category.name;
        document.getElementById('save-category-btn').textContent = 'Cập nhật danh mục';
    }
}

// Hàm xóa danh mục
function deleteCategory(categoryId) {
    console.log('Xóa danh mục có ID:', categoryId);

    // Hiển thị modal xác nhận xóa
    const confirmModal = document.getElementById('confirm-modal');
    const confirmMessage = document.getElementById('confirm-message');
    confirmMessage.textContent = 'Bạn có chắc chắn muốn xóa danh mục này?';

    // Hiển thị modal
    confirmModal.style.display = 'flex';

    // Xử lý sự kiện nút xác nhận
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.onclick = async () => {
        try {
            // Gọi API xóa danh mục
            const response = await fetch(`${API_URL}/menu/categories/${categoryId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Không thể xóa danh mục');
            }

            // Đóng modal
            confirmModal.style.display = 'none';

            // Tải lại danh sách danh mục
            await manageCategories();

            // Tải lại dữ liệu menu
            await loadMenuData();

            alert('Xóa danh mục thành công');

        } catch (error) {
            console.error('Lỗi khi xóa danh mục:', error);
            alert('Lỗi khi xóa danh mục: ' + error.message);

            // Đóng modal
            confirmModal.style.display = 'none';
        }
    };

    // Xử lý sự kiện nút hủy
    const cancelBtn = document.getElementById('cancel-confirm-btn');
    cancelBtn.onclick = () => {
        confirmModal.style.display = 'none';
    };
}

// Hàm xem chi tiết đơn hàng
async function viewOrder(orderId) {
    console.log('Xem chi tiết đơn hàng có ID:', orderId);

    try {
        // Lấy thông tin chi tiết đơn hàng
        const response = await fetch(`${API_URL}/orders/${orderId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy thông tin đơn hàng');
        }

        const order = await response.json();
        console.log('Chi tiết đơn hàng:', order);

        // Hiển thị thông tin đơn hàng trong modal
        document.getElementById('order-detail-id').textContent = `#${order.id}`;
        document.getElementById('order-detail-table').textContent = order.table_name || 'N/A';
        document.getElementById('order-detail-time').textContent = order.order_time ? new Date(order.order_time).toLocaleString() : 'N/A';
        document.getElementById('order-detail-status').textContent = order.status || 'Chưa xác định';
        document.getElementById('order-detail-status').className = order.status === 'Đã thanh toán' ? 'status completed' : 'status pending';
        document.getElementById('order-detail-total').textContent = formatCurrency(order.total);



        // Hiển thị danh sách món ăn
        const orderItemsTable = document.getElementById('order-detail-items');
        orderItemsTable.innerHTML = '';

        if (!order.items || order.items.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="4" class="text-center">Không có món ăn nào</td>';
            orderItemsTable.appendChild(row);
        } else {
            order.items.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.food_name}</td>
                    <td>${item.quantity}</td>
                    <td>${formatCurrency(item.price)}</td>
                    <td>${formatCurrency(item.price * item.quantity)}</td>
                `;
                orderItemsTable.appendChild(row);
            });
        }

        // Thêm sự kiện cho nút in đơn hàng
        document.getElementById('print-order-detail-btn').onclick = () => {
            printOrder(orderId);
        };

        // Thêm sự kiện cho nút đóng
        document.getElementById('close-order-detail-btn').onclick = () => {
            document.getElementById('order-detail-modal').style.display = 'none';
        };

        // Thêm sự kiện cho nút đóng (X)
        document.querySelector('#order-detail-modal .close-btn').onclick = () => {
            document.getElementById('order-detail-modal').style.display = 'none';
        };

        // Hiển thị modal
        document.getElementById('order-detail-modal').style.display = 'flex';

    } catch (error) {
        console.error('Lỗi khi lấy thông tin đơn hàng:', error);
        alert('Lỗi khi lấy thông tin đơn hàng: ' + error.message);
    }
}

// Hàm in đơn hàng
async function printOrder(orderId) {
    console.log('In đơn hàng có ID:', orderId);

    try {
        // Lấy thông tin chi tiết đơn hàng
        const response = await fetch(`${API_URL}/orders/${orderId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy thông tin đơn hàng');
        }

        const order = await response.json();
        console.log('Chi tiết đơn hàng để in:', order);

        // Tạo nội dung xem trước khi in
        const printContent = document.getElementById('print-preview-content');

        // Lấy ngày giờ hiện tại
        const now = new Date();
        const printDate = now.toLocaleDateString();
        const printTime = now.toLocaleTimeString();

        // Tạo HTML cho nội dung in
        printContent.innerHTML = `
            <div class="print-header">
                <h1>HÓA ĐƠN THANH TOÁN</h1>
                <p>Nhà hàng XYZ</p>
                <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
                <p>Điện thoại: 0123 456 789</p>
            </div>
            <div class="print-info">
                <div class="print-info-row">
                    <div class="print-info-label">Mã hóa đơn:</div>
                    <div class="print-info-value">#${order.id}</div>
                </div>
                <div class="print-info-row">
                    <div class="print-info-label">Bàn:</div>
                    <div class="print-info-value">${order.table_name || 'N/A'}</div>
                </div>
                <div class="print-info-row">
                    <div class="print-info-label">Thời gian:</div>
                    <div class="print-info-value">${order.order_time ? new Date(order.order_time).toLocaleString() : 'N/A'}</div>
                </div>
                <div class="print-info-row">
                    <div class="print-info-label">Ngày in:</div>
                    <div class="print-info-value">${printDate} ${printTime}</div>
                </div>
            </div>
            <table class="print-table">
                <thead>
                    <tr>
                        <th>STT</th>
                        <th>Tên món</th>
                        <th>Số lượng</th>
                        <th>Đơn giá</th>
                        <th>Thành tiền</th>
                    </tr>
                </thead>
                <tbody>
                    ${order.items && order.items.length > 0 ?
                        order.items.map((item, index) => `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${item.food_name}</td>
                                <td>${item.quantity}</td>
                                <td>${formatCurrency(item.price)}</td>
                                <td>${formatCurrency(item.price * item.quantity)}</td>
                            </tr>
                        `).join('') :
                        '<tr><td colspan="5" style="text-align: center;">Không có món ăn nào</td></tr>'
                    }
                </tbody>
            </table>
            <div class="print-total">
                Tổng cộng: ${formatCurrency(order.total)}
            </div>
            <div class="print-footer">
                <p>Cảm ơn quý khách đã sử dụng dịch vụ!</p>
                <p>Hẹn gặp lại quý khách!</p>
            </div>
        `;

        // Thêm sự kiện cho nút đóng
        document.getElementById('close-print-preview-btn').onclick = () => {
            document.getElementById('print-preview-modal').style.display = 'none';
        };

        // Thêm sự kiện cho nút đóng (X)
        document.querySelector('#print-preview-modal .close-btn').onclick = () => {
            document.getElementById('print-preview-modal').style.display = 'none';
        };

        // Thêm sự kiện cho nút in
        document.getElementById('confirm-print-btn').onclick = () => {
            // Ẩn modal
            document.getElementById('print-preview-modal').style.display = 'none';

            // Tạo một cửa sổ in mới
            const printWindow = window.open('', '_blank');

            // Tạo nội dung HTML cho cửa sổ in
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Hóa đơn #${order.id}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                        }
                        .print-header {
                            text-align: center;
                            margin-bottom: 20px;
                            padding-bottom: 20px;
                            border-bottom: 1px solid #ddd;
                        }
                        .print-header h1 {
                            font-size: 24px;
                            margin-bottom: 5px;
                        }
                        .print-header p {
                            color: #666;
                            margin: 5px 0;
                        }
                        .print-info {
                            margin-bottom: 20px;
                        }
                        .print-info-row {
                            display: flex;
                            margin-bottom: 5px;
                        }
                        .print-info-label {
                            font-weight: bold;
                            width: 120px;
                        }
                        .print-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 20px;
                        }
                        .print-table th, .print-table td {
                            padding: 8px;
                            text-align: left;
                            border-bottom: 1px solid #ddd;
                        }
                        .print-table th {
                            background-color: #f4f4f4;
                        }
                        .print-total {
                            text-align: right;
                            font-weight: bold;
                            font-size: 18px;
                            margin-top: 20px;
                            padding-top: 10px;
                            border-top: 1px solid #ddd;
                        }
                        .print-footer {
                            text-align: center;
                            margin-top: 30px;
                            padding-top: 20px;
                            border-top: 1px solid #ddd;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    ${printContent.innerHTML}
                </body>
                </html>
            `);

            // Đóng document để hoàn tất việc viết
            printWindow.document.close();

            // Chờ tải trang xong và in
            printWindow.onload = function() {
                printWindow.print();
                // printWindow.close(); // Tùy chọn: đóng cửa sổ sau khi in
            };
        };

        // Hiển thị modal xem trước khi in
        document.getElementById('print-preview-modal').style.display = 'flex';

    } catch (error) {
        console.error('Lỗi khi in đơn hàng:', error);
        alert('Lỗi khi in đơn hàng: ' + error.message);
    }
}

// Hàm chỉnh sửa nguyên liệu
async function editIngredient(ingredientId) {
    console.log('Chỉnh sửa nguyên liệu có ID:', ingredientId);

    try {
        // Lấy thông tin nguyên liệu
        const response = await fetch(`${API_URL}/inventory/ingredients/${ingredientId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy thông tin nguyên liệu');
        }

        const ingredient = await response.json();
        console.log('Thông tin nguyên liệu:', ingredient);

        // Hiển thị modal chỉnh sửa nguyên liệu
        document.getElementById('ingredient-modal-title').textContent = 'Chỉnh sửa nguyên liệu';
        document.getElementById('ingredient-id').value = ingredient.id;
        document.getElementById('ingredient-name').value = ingredient.name;
        document.getElementById('ingredient-unit').value = ingredient.unit;
        document.getElementById('ingredient-quantity').value = ingredient.quantity;

        // Thêm sự kiện cho nút hủy và nút đóng modal
        document.getElementById('cancel-ingredient-btn').addEventListener('click', () => {
            document.getElementById('ingredient-modal').style.display = 'none';
        });

        document.querySelector('#ingredient-modal .close-btn').addEventListener('click', () => {
            document.getElementById('ingredient-modal').style.display = 'none';
        });

        // Thêm sự kiện submit form nguyên liệu
        document.getElementById('ingredient-form').addEventListener('submit', handleIngredientFormSubmit);

        // Hiển thị modal
        document.getElementById('ingredient-modal').style.display = 'flex';
    } catch (error) {
        console.error('Lỗi khi lấy thông tin nguyên liệu:', error);
        alert('Lỗi khi lấy thông tin nguyên liệu: ' + error.message);
    }
}

// Hàm xóa nguyên liệu
function deleteIngredient(ingredientId) {
    console.log('Xóa nguyên liệu có ID:', ingredientId);

    // Hiển thị modal xác nhận xóa
    const confirmModal = document.getElementById('confirm-modal');
    const confirmMessage = document.getElementById('confirm-message');
    confirmMessage.textContent = 'Bạn có chắc chắn muốn xóa nguyên liệu này?';

    // Hiển thị modal
    confirmModal.style.display = 'flex';

    // Xử lý sự kiện nút xác nhận
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.onclick = async () => {
        try {
            // Gọi API xóa nguyên liệu
            const response = await fetch(`${API_URL}/inventory/ingredients/${ingredientId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Không thể xóa nguyên liệu');
            }

            // Đóng modal
            confirmModal.style.display = 'none';

            // Tải lại dữ liệu kho hàng
            await loadInventoryData();

            alert('Xóa nguyên liệu thành công');
        } catch (error) {
            console.error('Lỗi khi xóa nguyên liệu:', error);
            alert('Lỗi khi xóa nguyên liệu: ' + error.message);

            // Đóng modal
            confirmModal.style.display = 'none';
        }
    };
}

// Hàm thêm nguyên liệu mới
function addIngredient() {
    console.log('Thêm nguyên liệu mới');

    // Hiển thị modal thêm nguyên liệu
    document.getElementById('ingredient-modal-title').textContent = 'Thêm nguyên liệu mới';
    document.getElementById('ingredient-id').value = '';
    document.getElementById('ingredient-name').value = '';
    document.getElementById('ingredient-unit').value = '';
    document.getElementById('ingredient-quantity').value = '0';

    // Thêm sự kiện cho nút hủy và nút đóng modal
    document.getElementById('cancel-ingredient-btn').addEventListener('click', () => {
        document.getElementById('ingredient-modal').style.display = 'none';
    });

    document.querySelector('#ingredient-modal .close-btn').addEventListener('click', () => {
        document.getElementById('ingredient-modal').style.display = 'none';
    });

    // Thêm sự kiện submit form nguyên liệu
    document.getElementById('ingredient-form').addEventListener('submit', handleIngredientFormSubmit);

    // Hiển thị modal
    document.getElementById('ingredient-modal').style.display = 'flex';
}

// Hàm xử lý submit form nguyên liệu
async function handleIngredientFormSubmit(event) {
    event.preventDefault();

    const ingredientId = document.getElementById('ingredient-id').value;
    const isNewIngredient = !ingredientId;

    try {
        const ingredientData = {
            name: document.getElementById('ingredient-name').value,
            unit: document.getElementById('ingredient-unit').value,
            quantity: parseFloat(document.getElementById('ingredient-quantity').value)
        };

        let response;
        if (isNewIngredient) {
            // Thêm nguyên liệu mới
            response = await fetch(`${API_URL}/inventory/ingredients`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(ingredientData)
            });
        } else {
            // Cập nhật nguyên liệu
            response = await fetch(`${API_URL}/inventory/ingredients/${ingredientId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(ingredientData)
            });
        }

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Lỗi khi lưu thông tin nguyên liệu');
        }

        // Đóng modal
        document.getElementById('ingredient-modal').style.display = 'none';

        // Tải lại dữ liệu kho hàng
        await loadInventoryData();

        alert(isNewIngredient ? 'Thêm nguyên liệu thành công' : 'Cập nhật nguyên liệu thành công');
    } catch (error) {
        console.error('Lỗi khi lưu thông tin nguyên liệu:', error);
        alert('Lỗi khi lưu thông tin nguyên liệu: ' + error.message);
    }
}

// Hàm lọc món ăn theo danh mục
function filterFoodsByCategory(categoryId) {
    console.log('Lọc món ăn theo danh mục:', categoryId);

    const menuContainer = document.getElementById('menu-container');
    menuContainer.innerHTML = '';

    let filteredFoods = foods;

    // Nếu chọn danh mục cụ thể (không phải "Tất cả danh mục")
    if (categoryId !== 'all') {
        filteredFoods = foods.filter(food => food.category_id === parseInt(categoryId));
    }

    if (filteredFoods.length === 0) {
        menuContainer.innerHTML = `
            <div class="empty-menu">
                <i class="fas fa-filter"></i>
                <h3>Không có món ăn nào trong danh mục này</h3>
                <p>Hãy thêm món ăn mới hoặc chọn danh mục khác</p>
                <button class="btn primary" id="add-empty-food-btn">
                    <i class="fas fa-plus"></i> Thêm món ăn
                </button>
            </div>
        `;

        // Thêm sự kiện cho nút thêm món ăn khi không có món ăn nào
        document.getElementById('add-empty-food-btn').addEventListener('click', () => {
            addFood();
        });

        return;
    }

    filteredFoods.forEach(food => {
        const foodCard = document.createElement('div');
        foodCard.className = 'food-card';

        // Tìm tên danh mục
        const category = categories.find(c => c.id === food.category_id);
        const categoryName = category ? category.name : 'Không có danh mục';

        // Xử lý URL hình ảnh
        let imageUrl = 'img/default-food.jpg';
        if (food.image_url) {
            // Kiểm tra xem image_url có phải là URL đầy đủ không
            if (food.image_url.startsWith('http')) {
                imageUrl = food.image_url;
            }
            // Kiểm tra xem image_url có phải là đường dẫn API không
            else if (food.image_url.startsWith('/api/images')) {
                imageUrl = 'http://localhost:3000' + food.image_url;
            }
            // Kiểm tra xem image_url có phải là đường dẫn uploads không
            else if (food.image_url.includes('/uploads/') || food.image_url.includes('/foods/')) {
                imageUrl = 'http://localhost:3000/api/images/foods/' + food.image_url.split('/').pop();
            }

            console.log('Food ID:', food.id, 'Original URL:', food.image_url, 'Processed URL:', imageUrl);
        }

        foodCard.innerHTML = `
            <div class="food-select">
                <input type="checkbox" class="food-checkbox" data-id="${food.id}">
            </div>
            <div class="food-category-badge">${categoryName}</div>
            <div class="food-image" style="background-image: url('${imageUrl}')"></div>
            <div class="food-info">
                <div class="food-name">${food.name}</div>
                <div class="food-category"><i class="fas fa-tag"></i> ${categoryName}</div>
                <div class="food-price">${formatCurrency(food.price)}</div>
                <div class="food-actions">
                    <button class="btn edit-btn edit-food-btn" data-id="${food.id}">
                        <i class="fas fa-edit"></i> Sửa
                    </button>
                    <button class="btn delete-btn delete-food-btn" data-id="${food.id}">
                        <i class="fas fa-trash"></i> Xóa
                    </button>
                </div>
            </div>
        `;

        menuContainer.appendChild(foodCard);
    });

    // Thêm lại sự kiện cho các nút
    document.querySelectorAll('.edit-food-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const foodId = btn.getAttribute('data-id');
            editFood(foodId);
        });
    });

    document.querySelectorAll('.delete-food-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const foodId = btn.getAttribute('data-id');
            deleteFood(foodId);
        });
    });
}

// Hàm xem nguyên liệu sắp hết
async function viewLowStock() {
    console.log('Xem nguyên liệu sắp hết');

    try {
        // Lấy danh sách nguyên liệu sắp hết
        const response = await fetch(`${API_URL}/inventory/low-stock/list`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách nguyên liệu sắp hết');
        }

        const lowStockIngredients = await response.json();
        console.log('Danh sách nguyên liệu sắp hết:', lowStockIngredients);

        // Hiển thị danh sách nguyên liệu sắp hết
        const ingredientsTable = document.getElementById('ingredients-table');
        ingredientsTable.innerHTML = '';

        if (lowStockIngredients.length === 0) {
            alert('Không có nguyên liệu nào sắp hết!');
            // Tải lại dữ liệu kho hàng
            await loadInventoryData();
            return;
        }

        lowStockIngredients.forEach(ingredient => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${ingredient.id}</td>
                <td>${ingredient.name}</td>
                <td>${ingredient.unit}</td>
                <td>${ingredient.quantity}</td>
                <td>
                    <button class="btn small edit-ingredient-btn" data-id="${ingredient.id}"><i class="fas fa-edit"></i></button>
                    <button class="btn small danger delete-ingredient-btn" data-id="${ingredient.id}"><i class="fas fa-trash"></i></button>
                </td>
            `;
            ingredientsTable.appendChild(row);
        });

        // Thêm sự kiện cho các nút
        document.querySelectorAll('.edit-ingredient-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const ingredientId = btn.getAttribute('data-id');
                editIngredient(ingredientId);
            });
        });

        document.querySelectorAll('.delete-ingredient-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const ingredientId = btn.getAttribute('data-id');
                deleteIngredient(ingredientId);
            });
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách nguyên liệu sắp hết:', error);
        alert('Lỗi khi lấy danh sách nguyên liệu sắp hết: ' + error.message);
    }
}
async function loadMenuData() {
    if (!token) return;

    try {
        console.log('Đang tải dữ liệu menu...');

        // Lấy danh sách danh mục
        const categoriesResponse = await fetch(`${API_URL}/menu/categories`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!categoriesResponse.ok) {
            throw new Error('Không thể lấy danh sách danh mục');
        }

        categories = await categoriesResponse.json();
        console.log('Danh sách danh mục:', categories);

        // Cập nhật dropdown danh mục
        const categoryFilter = document.getElementById('category-filter');
        // Xóa tất cả các option trừ option đầu tiên
        while (categoryFilter.options.length > 1) {
            categoryFilter.remove(1);
        }

        // Thêm các danh mục vào dropdown
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categoryFilter.appendChild(option);
        });

        // Cập nhật dropdown danh mục trong modal thêm/sửa món ăn
        const foodCategory = document.getElementById('food-category');
        foodCategory.innerHTML = '';

        // Thêm option mặc định
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '-- Chọn danh mục --';
        foodCategory.appendChild(defaultOption);

        // Thêm các danh mục vào dropdown
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            foodCategory.appendChild(option);
        });

        // Lấy danh sách món ăn
        const foodsResponse = await fetch(`${API_URL}/menu/foods`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!foodsResponse.ok) {
            throw new Error('Không thể lấy danh sách món ăn');
        }

        foods = await foodsResponse.json();
        console.log('Danh sách món ăn:', foods);

        // Hiển thị danh sách món ăn
        const menuContainer = document.getElementById('menu-container');
        menuContainer.innerHTML = '';

        if (foods.length === 0) {
            menuContainer.innerHTML = `
                <div class="empty-menu">
                    <i class="fas fa-utensils"></i>
                    <h3>Không có món ăn nào</h3>
                    <p>Hãy thêm món ăn mới để hiển thị tại đây</p>
                    <button class="btn primary" id="add-empty-food-btn">
                        <i class="fas fa-plus"></i> Thêm món ăn
                    </button>
                </div>
            `;

            // Thêm sự kiện cho nút thêm món ăn khi không có món ăn nào
            document.getElementById('add-empty-food-btn').addEventListener('click', () => {
                addFood();
            });

            return;
        }

        foods.forEach(food => {
            const foodCard = document.createElement('div');
            foodCard.className = 'food-card';

            // Tìm tên danh mục
            const category = categories.find(c => c.id === food.category_id);
            const categoryName = category ? category.name : 'Không có danh mục';

            // Xử lý URL hình ảnh
            let imageUrl = 'img/default-food.jpg';
            if (food.image_url) {
                // Kiểm tra xem image_url có phải là URL đầy đủ không
                if (food.image_url.startsWith('http')) {
                    imageUrl = food.image_url;
                }
                // Kiểm tra xem image_url có phải là đường dẫn API không
                else if (food.image_url.startsWith('/api/images')) {
                    imageUrl = 'http://localhost:3000' + food.image_url;
                }
                // Kiểm tra xem image_url có phải là đường dẫn uploads không
                else if (food.image_url.includes('/uploads/') || food.image_url.includes('/foods/')) {
                    imageUrl = 'http://localhost:3000/api/images/foods/' + food.image_url.split('/').pop();
                }

                console.log('Food ID:', food.id, 'Original URL:', food.image_url, 'Processed URL:', imageUrl);
            }

            foodCard.innerHTML = `
                <div class="food-select">
                    <input type="checkbox" class="food-checkbox" data-id="${food.id}">
                </div>
                <div class="food-category-badge">${categoryName}</div>
                <div class="food-image" style="background-image: url('${imageUrl}')"></div>
                <div class="food-info">
                    <div class="food-name">${food.name}</div>
                    <div class="food-category"><i class="fas fa-tag"></i> ${categoryName}</div>
                    <div class="food-price">${formatCurrency(food.price)}</div>
                    <div class="food-actions">
                        <button class="btn edit-btn edit-food-btn" data-id="${food.id}">
                            <i class="fas fa-edit"></i> Sửa
                        </button>
                        <button class="btn delete-btn delete-food-btn" data-id="${food.id}">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </div>
                </div>
            `;

            menuContainer.appendChild(foodCard);
        });

        // Thêm sự kiện cho các nút
        document.querySelectorAll('.edit-food-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const foodId = btn.getAttribute('data-id');
                editFood(foodId);
            });
        });

        document.querySelectorAll('.delete-food-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const foodId = btn.getAttribute('data-id');
                deleteFood(foodId);
            });
        });

        // Thêm sự kiện cho nút thêm món ăn
        document.getElementById('add-food-btn').addEventListener('click', () => {
            addFood();
        });

        // Thêm sự kiện cho nút quản lý danh mục
        document.getElementById('manage-categories-btn').addEventListener('click', () => {
            manageCategories();
        });

        // Thêm sự kiện cho dropdown lọc danh mục
        document.getElementById('category-filter').addEventListener('change', function() {
            const selectedCategoryId = this.value;
            filterFoodsByCategory(selectedCategoryId);
        });

        // Thêm sự kiện cho nút đóng modal
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // Tìm modal cha
                const modal = btn.closest('.modal');
                modal.style.display = 'none';
            });
        });

        // Thêm sự kiện cho nút hủy trong modal thêm/sửa món ăn
        document.getElementById('cancel-food-btn').addEventListener('click', () => {
            document.getElementById('food-modal').style.display = 'none';
        });

        // Thêm sự kiện cho nút hủy trong modal quản lý danh mục
        document.getElementById('cancel-category-btn').addEventListener('click', () => {
            document.getElementById('categories-modal').style.display = 'none';
        });

        // Thêm sự kiện cho input file để xem trước hình ảnh
        document.getElementById('food-image-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    document.getElementById('food-image-preview-img').src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Thêm sự kiện cho form thêm/sửa món ăn (chỉ thêm một lần)
        const foodForm = document.getElementById('food-form');

        // Xóa event listener cũ nếu có
        const newFoodForm = foodForm.cloneNode(true);
        foodForm.parentNode.replaceChild(newFoodForm, foodForm);

        document.getElementById('food-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            // Ngăn chặn submit nhiều lần
            const submitButton = e.target.querySelector('button[type="submit"]');
            if (submitButton) {
                if (submitButton.disabled) return; // Đã đang xử lý
                submitButton.disabled = true;
                submitButton.textContent = 'Đang xử lý...';
            }

            const foodId = document.getElementById('food-id').value;
            const foodName = document.getElementById('food-name').value;
            const foodCategory = document.getElementById('food-category').value;
            const foodPrice = document.getElementById('food-price').value;
            const foodDescription = document.getElementById('food-description').value;
            const foodImageInput = document.getElementById('food-image-upload');
            const existingImageUrl = document.getElementById('food-image').value;

            try {
                // Sử dụng URL hình ảnh hiện tại nếu không có file mới
                let imageUrl = existingImageUrl;

                // Xử lý hình ảnh
                if (foodImageInput.files.length > 0) {
                    try {
                        // Tạo FormData để tải lên hình ảnh
                        const formData = new FormData();
                        formData.append('image', foodImageInput.files[0]);

                        // Hiển thị thông báo
                        console.log('Đang tải lên hình ảnh...');

                        // Gửi request tải lên hình ảnh
                        const imageResponse = await fetch(`${API_URL}/images/foods`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            body: formData
                        });

                        if (!imageResponse.ok) {
                            throw new Error('Không thể tải lên hình ảnh');
                        }

                        // Lấy URL hình ảnh từ response
                        const imageData = await imageResponse.json();
                        imageUrl = imageData.url;

                        console.log('Tải lên hình ảnh thành công:', imageUrl);

                        // Hiển thị hình ảnh xem trước
                        document.getElementById('food-image-preview-img').src = imageUrl;
                    } catch (imageError) {
                        console.error('Lỗi khi tải lên hình ảnh:', imageError);
                        // Sử dụng URL mặc định nếu có lỗi
                        imageUrl = '/img/default-food.jpg';
                    }
                }

                // Chuẩn bị dữ liệu món ăn
                const foodData = {
                    name: foodName,
                    category_id: foodCategory,
                    price: parseInt(foodPrice),
                    description: foodDescription,
                    image_url: imageUrl
                };

                console.log('Dữ liệu món ăn sẽ gửi đi:', {
                    name: foodData.name,
                    category_id: foodData.category_id,
                    price: foodData.price,
                    description: foodData.description,
                    image_url_length: foodData.image_url ? foodData.image_url.length : 0
                });

                let response;

                try {
                    if (foodId) {
                        // Cập nhật món ăn
                        console.log(`Đang gửi request PUT đến ${API_URL}/menu/foods/${foodId}`);
                        response = await fetch(`${API_URL}/menu/foods/${foodId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(foodData)
                        });
                    } else {
                        // Thêm món ăn mới
                        console.log(`Đang gửi request POST đến ${API_URL}/menu/foods`);
                        console.log('Dữ liệu gửi đi:', foodData);

                        // Tạo một bản sao của foodData để tránh tham chiếu
                        const foodDataCopy = JSON.parse(JSON.stringify(foodData));

                        response = await fetch(`${API_URL}/menu/foods`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(foodDataCopy)
                        });
                    }

                    console.log('Phản hồi từ server:', response.status);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Nội dung lỗi:', errorText);
                        throw new Error(errorText || 'Không thể lưu món ăn');
                    }
                } catch (fetchError) {
                    console.error('Lỗi khi gửi request:', fetchError);
                    throw fetchError;
                }

                // Kiểm tra response.ok đã được thực hiện trong khối try-catch ở trên

                // Đóng modal
                document.getElementById('food-modal').style.display = 'none';

                // Tải lại dữ liệu menu
                await loadMenuData();

                alert(foodId ? 'Cập nhật món ăn thành công' : 'Thêm món ăn mới thành công');

            } catch (error) {
                console.error('Lỗi khi lưu món ăn:', error);
                alert('Lỗi khi lưu món ăn: ' + error.message);
            } finally {
                // Reset button về trạng thái ban đầu
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = foodId ? 'Cập nhật' : 'Thêm món ăn';
                }
            }
        });

        // Thêm sự kiện cho form thêm/sửa danh mục (chỉ thêm một lần)
        const categoryForm = document.getElementById('category-form');

        // Xóa event listener cũ nếu có
        const newCategoryForm = categoryForm.cloneNode(true);
        categoryForm.parentNode.replaceChild(newCategoryForm, categoryForm);

        document.getElementById('category-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            // Ngăn chặn submit nhiều lần
            const submitButton = e.target.querySelector('button[type="submit"]');
            if (submitButton) {
                if (submitButton.disabled) return; // Đã đang xử lý
                submitButton.disabled = true;
                submitButton.textContent = 'Đang xử lý...';
            }

            const categoryId = document.getElementById('category-id').value;
            const categoryName = document.getElementById('category-name').value;

            const categoryData = {
                name: categoryName
            };

            try {
                let response;

                if (categoryId) {
                    // Cập nhật danh mục
                    response = await fetch(`${API_URL}/menu/categories/${categoryId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(categoryData)
                    });
                } else {
                    // Thêm danh mục mới
                    response = await fetch(`${API_URL}/menu/categories`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(categoryData)
                    });
                }

                if (!response.ok) {
                    throw new Error('Không thể lưu danh mục');
                }

                // Reset form
                document.getElementById('category-id').value = '';
                document.getElementById('category-name').value = '';
                document.getElementById('save-category-btn').textContent = 'Thêm danh mục';

                // Tải lại danh sách danh mục
                await manageCategories();

                // Tải lại dữ liệu menu
                await loadMenuData();

                alert(categoryId ? 'Cập nhật danh mục thành công' : 'Thêm danh mục mới thành công');

            } catch (error) {
                console.error('Lỗi khi lưu danh mục:', error);
                alert('Lỗi khi lưu danh mục: ' + error.message);
            } finally {
                // Reset button về trạng thái ban đầu
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = categoryId ? 'Cập nhật' : 'Thêm danh mục';
                }
            }
        });

    } catch (error) {
        console.error('Lỗi khi tải dữ liệu menu:', error);
        alert('Lỗi khi tải dữ liệu menu: ' + error.message);
    }
}

async function loadOrdersData() {
    if (!token) return;

    try {
        console.log('Đang tải dữ liệu đơn hàng...');

        // Lấy ngày hiện tại
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('from-date').value = today;
        document.getElementById('to-date').value = today;

        // Lấy tất cả đơn hàng (không lọc theo ngày)
        const response = await fetch(`${API_URL}/orders`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách đơn hàng');
        }

        const orders = await response.json();
        console.log('Danh sách đơn hàng:', orders);

        // Hiển thị danh sách đơn hàng
        displayOrders(orders);

        // Thêm sự kiện cho bộ lọc trạng thái
        document.getElementById('order-status-filter').addEventListener('change', function() {
            const statusFilter = this.value;
            const fromDate = document.getElementById('from-date').value;
            const toDate = document.getElementById('to-date').value;

            filterOrders(statusFilter, fromDate, toDate);
        });

        // Thêm sự kiện cho nút áp dụng bộ lọc ngày
        document.getElementById('apply-date-filter').addEventListener('click', () => {
            const statusFilter = document.getElementById('order-status-filter').value;
            const fromDate = document.getElementById('from-date').value;
            const toDate = document.getElementById('to-date').value;

            if (!fromDate || !toDate) {
                alert('Vui lòng chọn ngày bắt đầu và ngày kết thúc');
                return;
            }

            filterOrders(statusFilter, fromDate, toDate);
        });

    } catch (error) {
        console.error('Lỗi khi tải dữ liệu đơn hàng:', error);
        alert('Lỗi khi tải dữ liệu đơn hàng: ' + error.message);
    }
}

// Hiển thị danh sách đơn hàng
function displayOrders(orders) {
    const ordersTable = document.getElementById('orders-table');
    ordersTable.innerHTML = '';

    if (!orders || orders.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">Không có đơn hàng nào</td>';
        ordersTable.appendChild(row);

        // Log để debug
        console.log('Không có đơn hàng nào để hiển thị');
        return;
    }

    // Log để debug
    console.log(`Hiển thị ${orders.length} đơn hàng`);
    console.log('Dữ liệu đơn hàng:', orders);

    orders.forEach(order => {
        // Log thông tin chi tiết của từng đơn hàng
        console.log('Đơn hàng:', order);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${order.id || 'N/A'}</td>
            <td>${order.table_name || 'N/A'}</td>
            <td>${order.order_time ? new Date(order.order_time).toLocaleString() : 'N/A'}</td>
            <td>${order.total ? formatCurrency(order.total) : 'N/A'}</td>
            <td><span class="status ${order.status === 'Đã thanh toán' ? 'completed' : 'pending'}">${order.status || 'Chưa xác định'}</span></td>
            <td>
                <button class="btn small view-order-btn" data-id="${order.id}"><i class="fas fa-eye"></i></button>
                <button class="btn small primary print-order-btn" data-id="${order.id}"><i class="fas fa-print"></i></button>
                ${order.status !== 'Đã thanh toán' ?
                    `<button class="btn small success complete-order-btn" data-id="${order.id}" data-table-id="${order.table_id}"><i class="fas fa-check"></i></button>` :
                    ''
                }
            </td>
        `;
        ordersTable.appendChild(row);
    });

    // Thêm sự kiện cho các nút
    document.querySelectorAll('.view-order-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const orderId = btn.getAttribute('data-id');
            viewOrder(orderId);
        });
    });

    document.querySelectorAll('.print-order-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const orderId = btn.getAttribute('data-id');
            printOrder(orderId);
        });
    });

    document.querySelectorAll('.complete-order-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const orderId = btn.getAttribute('data-id');
            const tableId = btn.getAttribute('data-table-id');
            completeOrder(orderId, tableId);
        });
    });
}

// Lọc đơn hàng theo trạng thái và ngày
async function filterOrders(status, fromDate, toDate) {
    try {
        console.log('Lọc đơn hàng:', { status, fromDate, toDate });

        // Xây dựng URL với các tham số lọc
        let url = `${API_URL}/orders?`;

        // Thêm tham số lọc theo ngày nếu có
        if (fromDate && toDate) {
            url += `from_date=${fromDate}&to_date=${toDate}`;
        }

        // Thêm tham số lọc theo trạng thái nếu không phải "all"
        if (status && status !== 'all') {
            url += `&status=${status}`;
        }

        console.log('URL lọc đơn hàng:', url);

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách đơn hàng');
        }

        const orders = await response.json();
        console.log('Kết quả lọc đơn hàng:', orders);

        displayOrders(orders);
    } catch (error) {
        console.error('Lỗi khi lọc đơn hàng:', error);
        alert('Lỗi khi lọc đơn hàng: ' + error.message);
    }
}

// Hàm xác nhận hoàn thành đơn hàng
async function completeOrder(orderId, tableId) {
    try {
        // Hiển thị xác nhận
        const confirmed = confirm('Bạn có chắc chắn muốn xác nhận đơn hàng này đã hoàn thành? Hành động này sẽ vô hiệu hóa QR code của bàn.');

        if (!confirmed) {
            return;
        }

        console.log(`Xác nhận hoàn thành đơn hàng ${orderId} tại bàn ${tableId}`);

        // Cập nhật trạng thái đơn hàng thành "Đã thanh toán"
        const orderResponse = await fetch(`${API_URL}/orders/${orderId}/status`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: 'Đã thanh toán' })
        });

        if (!orderResponse.ok) {
            throw new Error('Không thể cập nhật trạng thái đơn hàng');
        }

        // Key sẽ được tự động vô hiệu hóa bởi Order Service khi status = "Đã thanh toán"

        alert('Đã xác nhận hoàn thành đơn hàng và vô hiệu hóa QR code của bàn');

        // Tải lại dữ liệu đơn hàng
        await loadOrdersData();

    } catch (error) {
        console.error('Lỗi khi xác nhận hoàn thành đơn hàng:', error);
        alert('Lỗi khi xác nhận hoàn thành đơn hàng: ' + error.message);
    }
}

async function loadKitchenData() {
    if (!token) return;

    try {
        console.log('Đang tải dữ liệu nhà bếp...');

        // Lấy thống kê nhà bếp
        const statsResponse = await fetch(`${API_URL}/kitchen/stats`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!statsResponse.ok) {
            throw new Error('Không thể lấy thống kê nhà bếp');
        }

        const stats = await statsResponse.json();
        console.log('Thống kê nhà bếp:', stats);

        // Cập nhật số liệu
        document.getElementById('waiting-count').textContent = stats.pending_count;
        document.getElementById('cooking-count').textContent = stats.cooking_count;
        document.getElementById('completed-count').textContent = stats.completed_count;

        // Lấy danh sách hàng đợi nhà bếp
        const queueResponse = await fetch(`${API_URL}/kitchen/queue`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!queueResponse.ok) {
            throw new Error('Không thể lấy danh sách hàng đợi nhà bếp');
        }

        const queueItems = await queueResponse.json();
        console.log('Danh sách hàng đợi nhà bếp:', queueItems);

        // Phân loại các món ăn theo trạng thái (handle encoding issues)
        console.log('All kitchen items:', queueItems);

        const waitingItems = queueItems.filter(item => {
            const isWaiting = item.status === 'Chờ chế biến' ||
                             item.status === 'Ch? ch? bi?n' ||
                             item.status.includes('Ch') ||
                             item.status.includes('ch?');
            console.log(`Item ${item.id} status: "${item.status}" -> waiting: ${isWaiting}`);
            return isWaiting;
        });

        const cookingItems = queueItems.filter(item => {
            const isCooking = item.status === 'Đang chế biến' ||
                             item.status === '?ang ch? bi?n' ||
                             item.status.includes('ang') ||
                             item.status.includes('?ang');
            return isCooking;
        });

        const completedItems = queueItems.filter(item => {
            const isCompleted = item.status === 'Hoàn thành' ||
                               item.status.includes('Hoàn') ||
                               item.status.includes('thành');
            return isCompleted;
        });

        console.log('Filtered items:', { waitingItems, cookingItems, completedItems });

        // Hiển thị danh sách món ăn đang chờ chế biến
        const waitingItemsContainer = document.getElementById('waiting-items');
        waitingItemsContainer.innerHTML = '';

        if (waitingItems.length === 0) {
            waitingItemsContainer.innerHTML = '<div class="empty-message">Không có món ăn nào đang chờ chế biến</div>';
        } else {
            waitingItems.forEach(item => {
                waitingItemsContainer.appendChild(createQueueItemElement(item, 'waiting'));
            });
        }

        // Hiển thị danh sách món ăn đang chế biến
        const cookingItemsContainer = document.getElementById('cooking-items-list');
        cookingItemsContainer.innerHTML = '';

        if (cookingItems.length === 0) {
            cookingItemsContainer.innerHTML = '<div class="empty-message">Không có món ăn nào đang chế biến</div>';
        } else {
            cookingItems.forEach(item => {
                cookingItemsContainer.appendChild(createQueueItemElement(item, 'cooking'));
            });
        }

        // Hiển thị danh sách món ăn đã hoàn thành
        const completedItemsContainer = document.getElementById('completed-items');
        completedItemsContainer.innerHTML = '';

        if (completedItems.length === 0) {
            completedItemsContainer.innerHTML = '<div class="empty-message">Không có món ăn nào đã hoàn thành</div>';
        } else {
            completedItems.forEach(item => {
                completedItemsContainer.appendChild(createQueueItemElement(item, 'completed'));
            });
        }

        // Thêm sự kiện cho các nút hành động
        // Bắt đầu chế biến
        document.querySelectorAll('.start-cooking-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const itemId = btn.getAttribute('data-id');
                await updateQueueItemStatus(itemId, 'Đang chế biến');
            });
        });

        // Hoàn thành chế biến
        document.querySelectorAll('.complete-cooking-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const itemId = btn.getAttribute('data-id');
                await updateQueueItemStatus(itemId, 'Hoàn thành');
            });
        });

        // Đã xóa nút "Quay lại" cho đơn hàng đã hoàn thành
        // Không cần event listener cho .revert-cooking-btn nữa

        // Thêm sự kiện cho nút làm mới
        const refreshBtn = document.getElementById('refresh-kitchen-btn');
        if (refreshBtn) {
            refreshBtn.removeEventListener('click', loadKitchenData); // Remove existing listener
            refreshBtn.addEventListener('click', loadKitchenData);
        }

        // Thêm sự kiện cho nút xóa món đã hoàn thành
        const clearCompletedBtn = document.getElementById('clear-completed-btn');
        if (clearCompletedBtn) {
            clearCompletedBtn.removeEventListener('click', clearCompletedItems); // Remove existing listener
            clearCompletedBtn.addEventListener('click', clearCompletedItems);
        }

    } catch (error) {
        console.error('Lỗi khi tải dữ liệu nhà bếp:', error);
        notificationManager.error('Lỗi khi tải dữ liệu nhà bếp: ' + error.message);
    }
}

// Hàm tạo phần tử hiển thị món ăn trong hàng đợi
function createQueueItemElement(item, type) {
    const queueItem = document.createElement('div');
    queueItem.className = 'queue-item';
    queueItem.dataset.id = item.id;

    // Tính thời gian đã trôi qua
    const updatedAt = new Date(item.updated_at);
    const now = new Date();
    const timeDiff = Math.floor((now - updatedAt) / 60000); // Số phút
    const timeText = timeDiff <= 0 ? 'Vừa xong' : `${timeDiff} phút trước`;

    // Tạo nội dung HTML cho phần tử
    queueItem.innerHTML = `
        <div class="queue-item-header">
            <div class="queue-item-table">${item.table_name}</div>
            <div class="queue-item-time">${timeText}</div>
        </div>
        <div class="queue-item-food">${item.food_name}</div>
        <div class="queue-item-quantity">Số lượng: ${item.quantity}</div>
        <div class="queue-item-actions">
            ${getActionButtonsForType(type, item.id)}
        </div>
    `;

    return queueItem;
}

// Hàm tạo các nút hành động dựa trên trạng thái
function getActionButtonsForType(type, itemId) {
    switch (type) {
        case 'waiting':
            return `<button class="btn secondary start-cooking-btn" data-id="${itemId}">Bắt đầu chế biến</button>`;
        case 'cooking':
            return `<button class="btn primary complete-cooking-btn" data-id="${itemId}">Hoàn thành</button>`;
        case 'completed':
            // Không hiển thị nút quay lại cho đơn hàng đã hoàn thành
            return '';
        default:
            return '';
    }
}

// Hàm cập nhật trạng thái món ăn
async function updateQueueItemStatus(itemId, newStatus) {
    try {
        console.log(`Cập nhật trạng thái món ăn ${itemId} thành ${newStatus}`);

        const response = await fetch(`${API_URL}/kitchen/queue/${itemId}/status`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) {
            throw new Error('Không thể cập nhật trạng thái món ăn');
        }

        // Tải lại dữ liệu nhà bếp
        await loadKitchenData();

    } catch (error) {
        console.error('Lỗi khi cập nhật trạng thái món ăn:', error);
        notificationManager.error('Lỗi khi cập nhật trạng thái món ăn: ' + error.message);
    }
}

// Hàm xóa tất cả món ăn đã hoàn thành
async function clearCompletedItems() {
    try {
        // Hiển thị modal xác nhận
        const confirmModal = document.getElementById('confirm-modal');
        const confirmMessage = document.getElementById('confirm-message');
        confirmMessage.textContent = 'Bạn có chắc chắn muốn xóa tất cả món ăn đã hoàn thành?';

        // Hiển thị modal
        confirmModal.style.display = 'flex';

        // Xử lý sự kiện nút xác nhận
        const confirmBtn = document.getElementById('confirm-btn');
        confirmBtn.onclick = async () => {
            try {
                console.log('Đang xóa tất cả món ăn đã hoàn thành...');

                const response = await fetch(`${API_URL}/kitchen/queue/completed`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Không thể xóa món ăn đã hoàn thành');
                }

                const result = await response.json();
                console.log('Kết quả xóa:', result);

                // Đóng modal
                confirmModal.style.display = 'none';

                // Hiển thị thông báo thành công
                notificationManager.success(result.message);

                // Tải lại dữ liệu nhà bếp
                await loadKitchenData();

            } catch (error) {
                console.error('Lỗi khi xóa món ăn đã hoàn thành:', error);
                notificationManager.error('Lỗi khi xóa món ăn đã hoàn thành: ' + error.message);

                // Đóng modal
                confirmModal.style.display = 'none';
            }
        };

        // Xử lý sự kiện nút hủy
        const cancelBtn = document.getElementById('cancel-confirm-btn');
        cancelBtn.onclick = () => {
            confirmModal.style.display = 'none';
        };

    } catch (error) {
        console.error('Lỗi khi xóa món ăn đã hoàn thành:', error);
        notificationManager.error('Lỗi khi xóa món ăn đã hoàn thành: ' + error.message);
    }
}

async function loadInventoryData() {
    if (!token) return;

    try {
        console.log('Đang tải dữ liệu kho hàng...');

        // Lấy danh sách nguyên liệu
        const response = await fetch(`${API_URL}/inventory/ingredients`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách nguyên liệu');
        }

        const ingredients = await response.json();
        console.log('Danh sách nguyên liệu:', ingredients);

        // Hiển thị danh sách nguyên liệu
        const ingredientsTable = document.getElementById('ingredients-table');
        ingredientsTable.innerHTML = '';

        ingredients.forEach(ingredient => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${ingredient.id}</td>
                <td>${ingredient.name}</td>
                <td>${ingredient.unit}</td>
                <td>${ingredient.quantity}</td>
                <td>
                    <button class="btn small edit-ingredient-btn" data-id="${ingredient.id}"><i class="fas fa-edit"></i></button>
                    <button class="btn small danger delete-ingredient-btn" data-id="${ingredient.id}"><i class="fas fa-trash"></i></button>
                </td>
            `;
            ingredientsTable.appendChild(row);
        });

        // Thêm sự kiện cho các nút
        document.querySelectorAll('.edit-ingredient-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const ingredientId = btn.getAttribute('data-id');
                editIngredient(ingredientId);
            });
        });

        document.querySelectorAll('.delete-ingredient-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const ingredientId = btn.getAttribute('data-id');
                deleteIngredient(ingredientId);
            });
        });

        // Thêm sự kiện cho nút thêm nguyên liệu
        document.getElementById('add-ingredient-btn').addEventListener('click', () => {
            addIngredient();
        });

        // Thêm sự kiện cho nút xem nguyên liệu sắp hết
        document.getElementById('low-stock-btn').addEventListener('click', () => {
            viewLowStock();
        });

    } catch (error) {
        console.error('Lỗi khi tải dữ liệu kho hàng:', error);
        alert('Lỗi khi tải dữ liệu kho hàng: ' + error.message);
    }
}

async function loadUsersData() {
    if (!token) return;

    try {
        console.log('Đang tải dữ liệu người dùng...');

        const response = await fetch(`${API_URL}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy danh sách người dùng');
        }

        const users = await response.json();
        console.log('Danh sách người dùng:', users);

        const usersTable = document.getElementById('users-table');
        usersTable.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.full_name || ''}</td>
                <td>${user.role_name}</td>
                <td>${user.email || ''}</td>
                <td>
                    <button class="btn small edit-user-btn" data-id="${user.id}"><i class="fas fa-edit"></i></button>
                    <button class="btn small danger delete-user-btn" data-id="${user.id}"><i class="fas fa-trash"></i></button>
                </td>
            `;
            usersTable.appendChild(row);
        });

        // Thêm sự kiện cho các nút
        document.querySelectorAll('.edit-user-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const userId = btn.getAttribute('data-id');
                editUser(userId);
            });
        });

        document.querySelectorAll('.delete-user-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const userId = btn.getAttribute('data-id');
                deleteUser(userId);
            });
        });

        // Thêm sự kiện cho nút thêm người dùng
        document.getElementById('add-user-btn').addEventListener('click', addUser);

        // Thêm sự kiện cho nút hủy trong modal người dùng
        document.getElementById('cancel-user-btn').addEventListener('click', () => {
            document.getElementById('user-modal').style.display = 'none';
        });

        // Thêm sự kiện cho nút đóng modal
        document.querySelector('#user-modal .close-btn').addEventListener('click', () => {
            document.getElementById('user-modal').style.display = 'none';
        });

        // Thêm sự kiện submit form người dùng
        document.getElementById('user-form').addEventListener('submit', handleUserFormSubmit);

        // Thêm sự kiện cho nút hủy trong modal xác nhận
        document.getElementById('cancel-confirm-btn').addEventListener('click', () => {
            document.getElementById('confirm-modal').style.display = 'none';
        });
    } catch (error) {
        console.error('Lỗi khi tải dữ liệu người dùng:', error);
        alert('Lỗi khi tải dữ liệu người dùng: ' + error.message);
    }
}

// Hàm thêm người dùng mới
function addUser() {
    console.log('Thêm người dùng mới');

    // Hiển thị modal thêm người dùng
    document.getElementById('user-modal-title').textContent = 'Thêm người dùng mới';
    document.getElementById('user-id').value = '';
    document.getElementById('user-username').value = '';
    document.getElementById('user-password').value = '';
    document.getElementById('user-role').value = '2'; // Mặc định là nhân viên
    document.getElementById('user-fullname').value = '';
    document.getElementById('user-email').value = '';
    document.getElementById('user-phone').value = '';
    document.getElementById('user-age').value = '';
    document.getElementById('user-address').value = '';

    // Hiển thị trường mật khẩu (bắt buộc khi thêm mới)
    document.querySelector('.password-group').style.display = 'block';
    document.getElementById('user-password').required = true;

    // Hiển thị modal
    document.getElementById('user-modal').style.display = 'flex';
}

// Hàm chỉnh sửa người dùng
async function editUser(userId) {
    console.log('Chỉnh sửa người dùng có ID:', userId);

    try {
        // Lấy thông tin người dùng
        const response = await fetch(`${API_URL}/users/${userId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể lấy thông tin người dùng');
        }

        const user = await response.json();
        console.log('Thông tin người dùng:', user);

        // Hiển thị modal chỉnh sửa người dùng
        document.getElementById('user-modal-title').textContent = 'Chỉnh sửa người dùng';
        document.getElementById('user-id').value = user.id;
        document.getElementById('user-username').value = user.username;
        document.getElementById('user-password').value = '';
        document.getElementById('user-role').value = user.role_id;
        document.getElementById('user-fullname').value = user.full_name || '';
        document.getElementById('user-email').value = user.email || '';
        document.getElementById('user-phone').value = user.phone_number || '';
        document.getElementById('user-age').value = user.age || '';
        document.getElementById('user-address').value = user.address || '';

        // Ẩn trường mật khẩu (không bắt buộc khi cập nhật)
        document.querySelector('.password-group').style.display = 'block';
        document.getElementById('user-password').required = false;

        // Hiển thị modal
        document.getElementById('user-modal').style.display = 'flex';
    } catch (error) {
        console.error('Lỗi khi lấy thông tin người dùng:', error);
        alert('Lỗi khi lấy thông tin người dùng: ' + error.message);
    }
}

// Hàm xử lý submit form người dùng
async function handleUserFormSubmit(event) {
    event.preventDefault();

    const userId = document.getElementById('user-id').value;
    const isNewUser = !userId;

    try {
        const userData = {
            username: document.getElementById('user-username').value,
            role_id: parseInt(document.getElementById('user-role').value),
            full_name: document.getElementById('user-fullname').value,
            email: document.getElementById('user-email').value,
            phone_number: document.getElementById('user-phone').value,
            age: document.getElementById('user-age').value ? parseInt(document.getElementById('user-age').value) : null,
            address: document.getElementById('user-address').value
        };

        // Thêm mật khẩu nếu có
        const password = document.getElementById('user-password').value;
        if (password) {
            userData.password = password;
        }

        let response;
        if (isNewUser) {
            // Thêm người dùng mới
            if (!userData.password) {
                throw new Error('Mật khẩu là bắt buộc khi thêm người dùng mới');
            }

            response = await fetch(`${API_URL}/users`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
        } else {
            // Cập nhật người dùng
            response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
        }

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Lỗi khi lưu thông tin người dùng');
        }

        // Đóng modal
        document.getElementById('user-modal').style.display = 'none';

        // Tải lại dữ liệu người dùng
        await loadUsersData();

        alert(isNewUser ? 'Thêm người dùng thành công' : 'Cập nhật người dùng thành công');
    } catch (error) {
        console.error('Lỗi khi lưu thông tin người dùng:', error);
        alert('Lỗi khi lưu thông tin người dùng: ' + error.message);
    }
}

// Hàm xóa người dùng
function deleteUser(userId) {
    console.log('Xóa người dùng có ID:', userId);

    // Hiển thị modal xác nhận xóa
    const confirmModal = document.getElementById('confirm-modal');
    const confirmMessage = document.getElementById('confirm-message');
    confirmMessage.textContent = 'Bạn có chắc chắn muốn xóa người dùng này?';

    // Hiển thị modal
    confirmModal.style.display = 'flex';

    // Xử lý sự kiện nút xác nhận
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.onclick = async () => {
        try {
            // Gọi API xóa người dùng
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Không thể xóa người dùng');
            }

            // Đóng modal
            confirmModal.style.display = 'none';

            // Tải lại dữ liệu người dùng
            await loadUsersData();

            alert('Xóa người dùng thành công');
        } catch (error) {
            console.error('Lỗi khi xóa người dùng:', error);
            alert('Lỗi khi xóa người dùng: ' + error.message);

            // Đóng modal
            confirmModal.style.display = 'none';
        }
    };
}

// Chỉnh sửa thông tin bàn
function editTable(tableId) {
    // Tìm thông tin bàn
    const table = tables.find(t => t.id == tableId);
    if (!table) {
        alert('Không tìm thấy thông tin bàn');
        return;
    }

    // Cập nhật tiêu đề modal
    document.getElementById('table-modal-title').textContent = `Chỉnh sửa bàn ${table.name}`;

    // Điền thông tin vào form
    document.getElementById('table-id').value = table.id;
    document.getElementById('table-name').value = table.name;
    document.getElementById('table-capacity').value = table.capacity || 4;
    document.getElementById('table-status').value = table.status || 'Trống';
    document.getElementById('table-location').value = table.location || 'Tầng 1';
    document.getElementById('table-note').value = table.note || '';

    // Hiển thị modal
    const modal = document.getElementById('table-modal');
    modal.style.display = 'flex';

    // Thêm sự kiện cho nút đóng
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.onclick = () => {
        modal.style.display = 'none';
    };

    // Thêm sự kiện cho nút hủy
    const cancelBtn = document.getElementById('cancel-table-btn');
    cancelBtn.onclick = () => {
        modal.style.display = 'none';
    };

    // Thêm sự kiện submit form
    const form = document.getElementById('table-form');
    form.onsubmit = async (e) => {
        e.preventDefault();

        // Lấy dữ liệu từ form
        const tableData = {
            name: document.getElementById('table-name').value,
            capacity: parseInt(document.getElementById('table-capacity').value),
            status: document.getElementById('table-status').value,
            location: document.getElementById('table-location').value,
            note: document.getElementById('table-note').value
        };

        try {
            // Gọi API để cập nhật thông tin bàn
            const response = await fetch(`${API_URL}/tables/${tableId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tableData)
            });

            if (!response.ok) {
                throw new Error('Không thể cập nhật thông tin bàn');
            }

            // Nếu admin chuyển trạng thái về "Trống", vô hiệu hóa keys
            if (tableData.status === 'Trống') {
                try {
                    console.log(`Vô hiệu hóa keys cho bàn ${tableId} vì admin chuyển về trạng thái Trống`);
                    const invalidateResponse = await fetch(`${API_URL}/tables/${tableId}/invalidate-keys`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (invalidateResponse.ok) {
                        console.log(`✅ Đã vô hiệu hóa keys cho bàn ${tableId}`);
                        notificationManager.success('Đã vô hiệu hóa QR code và chuyển bàn về trạng thái trống');
                    } else {
                        console.warn('Không thể vô hiệu hóa keys, nhưng bàn đã được cập nhật');
                    }
                } catch (keyError) {
                    console.error('Lỗi khi vô hiệu hóa keys:', keyError);
                    // Không throw error vì bàn đã được cập nhật thành công
                }
            }

            // Đóng modal
            modal.style.display = 'none';

            // Tải lại danh sách bàn
            await loadTablesData();

            // Hiển thị thông báo thành công
            notificationManager.success('Cập nhật thông tin bàn thành công');

        } catch (error) {
            console.error('Lỗi khi cập nhật thông tin bàn:', error);
            notificationManager.error('Lỗi khi cập nhật thông tin bàn: ' + error.message);
        }
    };
}

// Xem chi tiết bàn
async function viewTableDetail(tableId) {
    try {
        // Tìm thông tin bàn
        const table = tables.find(t => t.id == tableId);
        if (!table) {
            alert('Không tìm thấy thông tin bàn');
            return;
        }

        console.log('Xem chi tiết bàn:', table);

        // Điền thông tin vào modal
        document.getElementById('table-detail-name').textContent = table.name;
        document.getElementById('table-detail-capacity').textContent = `${table.capacity || 4} người`;
        document.getElementById('table-detail-status').textContent = table.status || 'Trống';
        document.getElementById('table-detail-status').className = table.status === 'Trống' ? 'status empty' : 'status occupied';
        document.getElementById('table-detail-location').textContent = table.location || 'Tầng 1';
        document.getElementById('table-detail-note').textContent = table.note || 'Không có ghi chú';

        // Kiểm tra xem bàn có đơn hàng hiện tại không
        const tableCurrentOrder = document.getElementById('table-current-order');
        const viewOrderBtn = document.getElementById('view-table-order-btn');

        if (table.status === 'Đang phục vụ' && table.current_order) {
            // Hiển thị thông tin đơn hàng hiện tại
            tableCurrentOrder.style.display = 'block';
            document.getElementById('table-order-id').textContent = `#${table.current_order.id}`;
            document.getElementById('table-order-time').textContent = new Date(table.current_order.order_time).toLocaleString();
            document.getElementById('table-order-total').textContent = formatCurrency(table.current_order.total);

            // Hiển thị nút xem đơn hàng
            viewOrderBtn.style.display = 'inline-block';
            viewOrderBtn.onclick = () => {
                // Đóng modal chi tiết bàn
                document.getElementById('table-detail-modal').style.display = 'none';

                // Mở modal chi tiết đơn hàng
                viewOrder(table.current_order.id);
            };
        } else {
            // Ẩn thông tin đơn hàng và nút xem đơn hàng
            tableCurrentOrder.style.display = 'none';
            viewOrderBtn.style.display = 'none';
        }

        // Thêm sự kiện cho nút tạo QR Code
        document.getElementById('generate-qr-btn').onclick = () => {
            generateQRCode(tableId);
        };

        // Thêm sự kiện cho nút chỉnh sửa
        document.getElementById('edit-table-btn').onclick = () => {
            // Đóng modal chi tiết bàn
            document.getElementById('table-detail-modal').style.display = 'none';

            // Mở modal chỉnh sửa bàn
            editTable(tableId);
        };

        // Thêm sự kiện cho nút xóa bàn
        document.getElementById('delete-table-btn').onclick = () => {
            // Đóng modal chi tiết bàn
            document.getElementById('table-detail-modal').style.display = 'none';

            // Mở modal xác nhận xóa bàn
            deleteTable(tableId);
        };

        // Thêm sự kiện cho nút đóng
        document.getElementById('close-table-detail-btn').onclick = () => {
            document.getElementById('table-detail-modal').style.display = 'none';
        };

        // Thêm sự kiện cho nút đóng (X)
        document.querySelector('#table-detail-modal .close-btn').onclick = () => {
            document.getElementById('table-detail-modal').style.display = 'none';
        };

        // Hiển thị modal
        document.getElementById('table-detail-modal').style.display = 'flex';

    } catch (error) {
        console.error('Lỗi khi xem chi tiết bàn:', error);
        alert('Lỗi khi xem chi tiết bàn: ' + error.message);
    }
}

// Xóa bàn
function deleteTable(tableId) {
    console.log('Xóa bàn có ID:', tableId);

    // Tìm thông tin bàn
    const table = tables.find(t => t.id == tableId);
    if (!table) {
        alert('Không tìm thấy thông tin bàn');
        return;
    }

    // Hiển thị modal xác nhận xóa
    const confirmModal = document.getElementById('confirm-modal');
    const confirmMessage = document.getElementById('confirm-message');
    confirmMessage.textContent = `Bạn có chắc chắn muốn xóa bàn ${table.name}?`;

    // Hiển thị modal
    confirmModal.style.display = 'flex';

    // Xử lý sự kiện nút xác nhận
    const confirmBtn = document.getElementById('confirm-btn');
    confirmBtn.onclick = async () => {
        try {
            // Gọi API xóa bàn
            const response = await fetch(`${API_URL}/tables/${tableId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Không thể xóa bàn');
            }

            // Đóng modal
            confirmModal.style.display = 'none';

            // Tải lại danh sách bàn
            await loadTablesData();

            alert('Xóa bàn thành công');

        } catch (error) {
            console.error('Lỗi khi xóa bàn:', error);
            alert('Lỗi khi xóa bàn: ' + error.message);

            // Đóng modal
            confirmModal.style.display = 'none';
        }
    };

    // Xử lý sự kiện nút hủy
    const cancelBtn = document.getElementById('cancel-confirm-btn');
    cancelBtn.onclick = () => {
        confirmModal.style.display = 'none';
    };
}

// Xem đơn hàng của bàn
async function viewTableOrder(tableId) {
    try {
        // Tìm thông tin bàn
        const table = tables.find(t => t.id == tableId);
        if (!table) {
            alert('Không tìm thấy thông tin bàn');
            return;
        }

        console.log('Xem đơn hàng của bàn:', table);

        // Kiểm tra xem bàn có đơn hàng hiện tại không
        if (table.status !== 'Đang phục vụ' || !table.current_order) {
            alert('Bàn này hiện không có đơn hàng');
            return;
        }

        // Mở modal chi tiết đơn hàng
        viewOrder(table.current_order.id);

    } catch (error) {
        console.error('Lỗi khi xem đơn hàng của bàn:', error);
        alert('Lỗi khi xem đơn hàng của bàn: ' + error.message);
    }
}

// Tạo QR code tĩnh cho web đặt món với kiểm tra trạng thái key
async function generateQRCode(tableId) {
    try {
        console.log('Kiểm tra trạng thái key cho bàn:', tableId);

        // Kiểm tra trạng thái key trước khi tạo QR
        const keyStatusResponse = await fetch(`${API_URL}/tables/${tableId}/key-status`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!keyStatusResponse.ok) {
            throw new Error('Không thể kiểm tra trạng thái bàn');
        }

        const keyStatus = await keyStatusResponse.json();
        console.log('Key status:', keyStatus);

        // Nếu bàn đã có key hợp lệ, không cho phép tạo QR mới
        if (!keyStatus.can_generate_qr) {
            const keyInfo = keyStatus.key_info;
            const expiresAt = new Date(keyInfo.expires_at).toLocaleString('vi-VN');

            const confirmMessage = `Bàn ${keyStatus.table_name} đã có QR code đang hoạt động!\n\n` +
                                 `🔑 Key hiện tại: ${keyInfo.key_value.substring(0, 8)}...\n` +
                                 `⏰ Hết hạn lúc: ${expiresAt}\n\n` +
                                 `Bạn có muốn vô hiệu hóa key cũ và tạo QR code mới không?`;

            if (!confirm(confirmMessage)) {
                return; // Người dùng không muốn tạo QR mới
            }

            // Vô hiệu hóa key cũ trước khi tạo mới
            console.log('Vô hiệu hóa key cũ...');
            const invalidateResponse = await fetch(`${API_URL}/tables/${tableId}/invalidate-keys`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!invalidateResponse.ok) {
                throw new Error('Không thể vô hiệu hóa key cũ');
            }

            console.log('Đã vô hiệu hóa key cũ thành công');
        }

        console.log('Tạo QR code cho bàn:', tableId);

        // Gọi API tạo QR code tĩnh
        const response = await fetch(`${API_URL}/tables/${tableId}/static-qrcode`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Không thể tạo QR code');
        }

        const qrData = await response.json();
        console.log('QR code data:', qrData);

        // Hiển thị QR code trong modal
        showQRCodeModal(qrData);

        // Tải lại dữ liệu bàn để cập nhật trạng thái
        loadTablesData();

    } catch (error) {
        console.error('Lỗi khi tạo QR code:', error);
        alert('Lỗi khi tạo QR code: ' + error.message);
    }
}

// Hiển thị modal QR code
function showQRCodeModal(qrData) {
    // Tạo modal QR code nếu chưa có
    let modal = document.getElementById('qr-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'qr-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close-btn">&times;</span>
                <h2>QR Code cho bàn ${qrData.table_name}</h2>
                <div class="qr-container">
                    <img id="qr-image" src="${qrData.qr_code}" alt="QR Code" style="max-width: 300px; max-height: 300px;">
                </div>
                <div class="qr-info">
                    <p><strong>Loại:</strong> QR Code tĩnh (dành cho in ấn)</p>
                    <p><strong>Link:</strong> <a href="${qrData.url}" target="_blank">${qrData.url}</a></p>
                    <p><em>Khách hàng có thể quét QR code này để truy cập web đặt món trực tiếp.</em></p>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="close-qr-btn">Đóng</button>
                    <button type="button" class="btn primary" id="download-qr-btn">Tải xuống</button>
                    <button type="button" class="btn primary" id="print-qr-btn">In QR Code</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    } else {
        // Cập nhật nội dung modal
        modal.querySelector('h2').textContent = `QR Code cho bàn ${qrData.table_name}`;
        modal.querySelector('#qr-image').src = qrData.qr_code;
        modal.querySelector('.qr-info a').href = qrData.url;
        modal.querySelector('.qr-info a').textContent = qrData.url;
    }

    // Thêm sự kiện cho các nút
    modal.querySelector('.close-btn').onclick = () => {
        modal.style.display = 'none';
    };

    modal.querySelector('#close-qr-btn').onclick = () => {
        modal.style.display = 'none';
    };

    modal.querySelector('#download-qr-btn').onclick = () => {
        downloadQRCode(qrData.qr_code, `QR_Ban_${qrData.table_name}.png`);
    };

    modal.querySelector('#print-qr-btn').onclick = () => {
        printQRCode(qrData);
    };

    // Hiển thị modal
    modal.style.display = 'flex';
}

// Tải xuống QR code
function downloadQRCode(qrCodeDataUrl, filename) {
    const link = document.createElement('a');
    link.href = qrCodeDataUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// In QR code
function printQRCode(qrData) {
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>QR Code - Bàn ${qrData.table_name}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    margin: 20px;
                    padding: 20px;
                }
                .qr-container {
                    margin: 20px 0;
                }
                .qr-image {
                    max-width: 300px;
                    max-height: 300px;
                    border: 2px solid #333;
                    padding: 10px;
                }
                .table-info {
                    font-size: 24px;
                    font-weight: bold;
                    margin: 20px 0;
                    color: #333;
                }
                .instructions {
                    font-size: 16px;
                    color: #666;
                    margin: 20px 0;
                    max-width: 400px;
                    margin-left: auto;
                    margin-right: auto;
                }
                .qr-info {
                    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
                    padding: 15px;
                    border-radius: 10px;
                    margin: 15px 0;
                    border-left: 4px solid #28a745;
                    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.1);
                }
                .qr-info p {
                    margin: 8px 0;
                    color: #155724;
                    font-size: 14px;
                }
                .restaurant-info {
                    margin-top: 30px;
                    font-size: 14px;
                    color: #888;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <h1>Nhà hàng ABC</h1>
            <div class="table-info">BÀN ${qrData.table_name}</div>
            <div class="qr-container">
                <img src="${qrData.qr_code}" alt="QR Code" class="qr-image">
            </div>
            <div class="instructions">
                <p><strong>Hướng dẫn sử dụng:</strong></p>
                <p>1. Mở camera hoặc ứng dụng quét QR code trên điện thoại</p>
                <p>2. Quét mã QR code này</p>
                <p>3. Hệ thống sẽ tự động tạo key mới và chuyển đến trang đặt món</p>
                <p>4. Chọn món yêu thích và gửi đơn hàng</p>
                <p>5. Key sẽ tự động vô hiệu hóa khi thanh toán xong</p>
            </div>
            <div class="qr-info">
                <p><strong>🔒 Bảo mật:</strong> Mỗi lần quét QR sẽ tạo key mới có thời hạn 2 giờ</p>
                <p><strong>🔄 Tự động:</strong> Key cũ sẽ bị vô hiệu hóa khi đơn hàng hoàn thành</p>
            </div>
            <div class="restaurant-info">
                <p>Cảm ơn quý khách đã sử dụng dịch vụ!</p>
                <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
                <p>Điện thoại: 0123 456 789</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Tự động in sau khi tải xong
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}

// Enhanced functions for better UX

// Get current page
function getCurrentPage() {
    const activePage = document.querySelector('.page[style*="block"]');
    return activePage ? activePage.id : 'dashboard';
}

// Setup auto-refresh for different pages
function setupAutoRefresh() {
    // Dashboard - refresh every 30 seconds
    autoRefreshManager.add('dashboard', () => {
        if (getCurrentPage() === 'dashboard') {
            loadDashboardData();
        }
    }, 30000);

    // Tables - refresh every 15 seconds
    autoRefreshManager.add('tables', () => {
        if (getCurrentPage() === 'tables') {
            loadTablesData();
        }
    }, 15000);

    // Kitchen - refresh every 10 seconds
    autoRefreshManager.add('kitchen', () => {
        if (getCurrentPage() === 'kitchen') {
            loadKitchenData();
        }
    }, 10000);

    // Orders - refresh every 20 seconds
    autoRefreshManager.add('orders', () => {
        if (getCurrentPage() === 'orders') {
            loadOrdersData();
        }
    }, 20000);
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
    // Ctrl+S to save (prevent default browser save)
    utils.keyboardShortcuts.add('ctrl+s', (e) => {
        e.preventDefault();
        notificationManager.info('Dữ liệu được tự động lưu');
    });

    // Ctrl+N to add new item
    utils.keyboardShortcuts.add('ctrl+n', (e) => {
        e.preventDefault();
        const currentPage = getCurrentPage();
        switch (currentPage) {
            case 'tables':
                addTable();
                break;
            case 'menu':
                addFood();
                break;
            case 'users':
                // Add user function if exists
                break;
        }
    });

    // F5 to refresh current page data
    utils.keyboardShortcuts.add('f5', (e) => {
        e.preventDefault();
        autoRefreshManager.refreshCurrentPage();
        notificationManager.success('Dữ liệu đã được làm mới');
    });
}

// Auto-refresh manager class
class AutoRefreshManager {
    constructor() {
        this.intervals = new Map();
        this.isVisible = true;
        this.init();
    }

    init() {
        // Listen for visibility changes
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                console.log('Page visible - resuming auto-refresh');
                this.refreshCurrentPage();
            } else {
                console.log('Page hidden - auto-refresh continues in background');
            }
        });
    }

    add(name, callback, interval) {
        this.remove(name); // Remove existing if any

        const intervalId = setInterval(() => {
            if (this.isVisible) {
                callback();
            }
        }, interval);

        this.intervals.set(name, intervalId);
    }

    remove(name) {
        const intervalId = this.intervals.get(name);
        if (intervalId) {
            clearInterval(intervalId);
            this.intervals.delete(name);
        }
    }

    refreshCurrentPage() {
        const currentPage = getCurrentPage();
        switch (currentPage) {
            case 'dashboard':
                loadDashboardData();
                break;
            case 'tables':
                loadTablesData();
                break;
            case 'menu':
                loadMenuData();
                break;
            case 'orders':
                loadOrdersData();
                break;
            case 'kitchen':
                loadKitchenData();
                break;
            case 'inventory':
                loadInventoryData();
                break;
            case 'users':
                loadUsersData();
                break;
        }
    }
}

// Initialize auto-refresh manager
const autoRefreshManager = new AutoRefreshManager();