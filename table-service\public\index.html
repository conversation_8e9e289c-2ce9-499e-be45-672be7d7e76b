<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Đặt món - Nhà hàng</title>
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <link rel="stylesheet" href="/public/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1><i class="fas fa-utensils"></i> Nhà hàng ABC</h1>
            <div class="table-info">
                <i class="fas fa-chair"></i>
                <span id="table-name">Đang tải...</span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav-tabs">
        <div class="container">
            <button class="tab-btn active" data-tab="menu">
                <i class="fas fa-utensils"></i>
                <span>Menu</span>
            </button>
            <button class="tab-btn" data-tab="cart">
                <i class="fas fa-shopping-cart"></i>
                <span>Giỏ hàng</span>
                <span class="cart-count" id="cart-count">0</span>
            </button>
            <button class="tab-btn" data-tab="orders">
                <i class="fas fa-receipt"></i>
                <span>Đơn hàng</span>
            </button>
        </div>
    </nav>

    <!-- Loading -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Đang tải menu...</p>
    </div>

    <!-- Menu Tab -->
    <div id="menu-tab" class="tab-content active">
        <div class="container">
            <!-- Order Type Selection -->
            <div class="order-type-selection" id="order-type-selection">
                <h3><i class="fas fa-utensils"></i> Chọn loại đặt món</h3>
                <div class="order-type-options">
                    <div class="order-type-card" id="normal-order-card">
                        <div class="order-type-icon">
                            <i class="fas fa-list-ul"></i>
                        </div>
                        <h4>Đặt món thông thường</h4>
                        <p>Chọn từng món ăn theo ý muốn</p>
                        <button class="select-type-btn" data-type="normal">Chọn</button>
                    </div>
                    <div class="order-type-card" id="buffet-order-card">
                        <div class="order-type-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <h4>Buffet</h4>
                        <p id="buffet-description">Ăn thoải mái tất cả món ăn</p>
                        <div class="buffet-price" id="buffet-price">Đang tải giá...</div>
                        <button class="select-type-btn" data-type="buffet">Chọn</button>
                    </div>
                </div>
            </div>

            <!-- Normal Order Section -->
            <div id="normal-order-section" class="order-section" style="display: none;">
                <div class="section-header">
                    <h3><i class="fas fa-list-ul"></i> Đặt món thông thường</h3>
                    <button class="back-to-selection-btn" id="back-to-selection">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </button>
                </div>

                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="Tìm kiếm món ăn...">
                </div>

                <div id="menu-categories" class="menu-categories">
                    <!-- Categories will be loaded here -->
                </div>
            </div>

            <!-- Buffet Order Section -->
            <div id="buffet-order-section" class="order-section" style="display: none;">
                <div class="section-header">
                    <h3><i class="fas fa-infinity"></i> Chọn gói Buffet</h3>
                    <button class="back-to-selection-btn" id="back-to-selection-buffet">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </button>
                </div>

                <!-- Buffet Packages Grid -->
                <div class="buffet-packages-grid" id="buffet-packages-grid">
                    <!-- Buffet packages will be loaded here -->
                </div>

                <!-- Selected Buffet Details -->
                <div class="buffet-info" id="buffet-selection" style="display: none;">
                    <div class="buffet-details">
                        <h4 id="selected-buffet-name">Thông tin Buffet</h4>
                        <div id="selected-buffet-features">
                            <!-- Features will be loaded here -->
                        </div>
                        <div class="buffet-price-display">
                            <span>Giá: </span>
                            <span class="price" id="buffet-price-display">Đang tải...</span>
                        </div>

                        <!-- Free Drink Selection - Hidden for now -->
                        <div class="free-drink-section" style="display: none;">
                            <h5><i class="fas fa-gift"></i> Chọn 1 món nước miễn phí</h5>
                            <div class="drinks-grid" id="free-drinks-grid">
                                <!-- Drinks will be loaded here -->
                            </div>
                        </div>
                    </div>
                    <div class="buffet-actions">
                        <button class="back-to-packages-btn" id="back-to-packages-btn">
                            <i class="fas fa-arrow-left"></i> Chọn gói khác
                        </button>
                        <button class="order-buffet-btn" id="order-buffet-btn" disabled>
                            <i class="fas fa-utensils"></i> Đặt Buffet
                        </button>
                    </div>
                </div>

                <!-- Buffet Active Section -->
                <div class="buffet-active" id="buffet-active" style="display: none;">
                    <div class="buffet-status">
                        <div class="buffet-status-header">
                            <i class="fas fa-infinity"></i>
                            <h4>Buffet đang hoạt động</h4>
                        </div>
                        <p>Bạn có thể gọi thêm món ăn bất kỳ (không bao gồm đồ uống)</p>
                    </div>

                    <!-- Food Categories for Buffet -->
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="buffet-search-input" placeholder="Tìm kiếm món ăn...">
                    </div>

                    <div id="buffet-food-categories" class="menu-categories">
                        <!-- Food categories will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart Tab -->
    <div id="cart-tab" class="tab-content">
        <div class="container">
            <div class="cart-header">
                <h2><i class="fas fa-shopping-cart"></i> Giỏ hàng của bạn</h2>
            </div>

            <div id="cart-items" class="cart-items">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Giỏ hàng trống</p>
                    <p>Hãy chọn món ăn từ menu</p>
                </div>
            </div>

            <div class="cart-summary" id="cart-summary" style="display: none;">
                <div class="total-row">
                    <span>Tổng cộng:</span>
                    <span class="total-amount" id="total-amount">0 ₫</span>
                </div>
                <button class="order-btn" id="place-order-btn">
                    <i class="fas fa-paper-plane"></i> Đặt món
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Tab -->
    <div id="orders-tab" class="tab-content">
        <div class="container">
            <div class="orders-header">
                <h2><i class="fas fa-receipt"></i> Đơn hàng của bạn</h2>
                <button class="refresh-btn" id="refresh-orders">
                    <i class="fas fa-sync-alt"></i> Làm mới
                </button>
            </div>

            <div id="current-order" class="current-order">
                <div class="no-orders">
                    <i class="fas fa-receipt"></i>
                    <p>Chưa có đơn hàng nào</p>
                    <p>Hãy đặt món từ menu</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Food Detail Modal -->
    <div id="food-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="food-detail">
                <div class="modal-image-container">
                    <img id="modal-food-image" src="" alt=""
                         onload="this.style.display='block'"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                    <div class="modal-no-image-placeholder" style="display:none">
                        <i class="fas fa-image"></i>
                        <span>Không có hình ảnh</span>
                    </div>
                </div>
                <div class="food-info">
                    <h3 id="modal-food-name"></h3>
                    <p class="food-price" id="modal-food-price"></p>
                    <div class="quantity-selector">
                        <button class="qty-btn" id="decrease-qty">-</button>
                        <span class="quantity" id="modal-quantity">1</span>
                        <button class="qty-btn" id="increase-qty">+</button>
                    </div>
                    <button class="add-to-cart-btn" id="add-to-cart-btn">
                        <i class="fas fa-plus"></i> Thêm vào giỏ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content payment-modal-content">
            <span class="close" onclick="closePaymentModal()">&times;</span>
            <div class="payment-header">
                <h3><i class="fas fa-credit-card"></i> Thanh toán đơn hàng</h3>
                <div class="payment-order-info">
                    <p>Đơn hàng: #<span id="payment-order-id"></span></p>
                    <p class="payment-total">Tổng tiền: <span id="payment-amount"></span></p>
                    <input type="hidden" id="payment-amount-value">
                </div>
            </div>

            <div class="payment-methods">
                <h4>Chọn phương thức thanh toán:</h4>

                <div class="payment-method-option">
                    <input type="radio" id="cash-payment" name="payment-method" value="cash" onchange="selectPaymentMethod('cash')">
                    <label for="cash-payment" class="payment-method-label">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Tiền mặt</span>
                    </label>
                </div>

                <div class="payment-method-option">
                    <input type="radio" id="momo-payment" name="payment-method" value="momo" onchange="selectPaymentMethod('momo')">
                    <label for="momo-payment" class="payment-method-label">
                        <i class="fas fa-mobile-alt"></i>
                        <span>MoMo</span>
                    </label>
                </div>
            </div>

            <!-- Cash Payment Section -->
            <div id="cash-payment-section" class="payment-section" style="display: none;">
                <h4><i class="fas fa-money-bill-wave"></i> Thanh toán tiền mặt</h4>
                <div class="cash-input-group">
                    <label for="cash-received">Số tiền nhận:</label>
                    <input type="number" id="cash-received" placeholder="Nhập số tiền nhận được" min="0" step="1000">
                    <small>Nhập số tiền khách đưa</small>
                </div>
            </div>

            <!-- MoMo Payment Section -->
            <div id="momo-payment-section" class="payment-section" style="display: none;">
                <h4><i class="fas fa-mobile-alt"></i> Thanh toán MoMo</h4>
                <div class="momo-info">
                    <p><i class="fas fa-info-circle"></i> Bạn sẽ được chuyển đến trang thanh toán MoMo</p>
                    <p><i class="fas fa-shield-alt"></i> Thanh toán an toàn và bảo mật</p>
                </div>
            </div>

            <div class="payment-actions">
                <button class="cancel-payment-btn" onclick="closePaymentModal()">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button class="process-payment-btn" onclick="processPayment()">
                    <i class="fas fa-check"></i> Thanh toán
                </button>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span id="notification-message"></span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/public/js/app.js"></script>
</body>
</html>
