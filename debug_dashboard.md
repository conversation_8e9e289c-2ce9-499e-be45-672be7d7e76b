# Debug Dashboard Data Issues

## Vấn đề đã phát hiện:

1. **Dữ liệu hiển thị 0 hoặc không chính xác** trong phần tổng quan
2. **Cần kiểm tra các API endpoints** để đảm bảo dữ liệu được trả về đúng

## Các bước debug:

### 1. Kiểm tra Console Log
Mở Developer Tools (F12) trong browser và kiểm tra Console tab để xem:
- `Tables data:` - Dữ liệu bàn
- `Today orders data:` - Dữ liệu đơn hàng hôm nay  
- `Tables for empty count:` - Dữ liệu để đếm bàn trống
- `Storage stats:` - Thống kê storage

### 2. Kiểm tra Network Tab
Trong Developer Tools, tab Network:
- Xem các API calls được gọi
- Kiểm tra response data
- Xem có lỗi 404, 500 không

### 3. Các API endpoints cần kiểm tra:

```
GET /api/tables - L<PERSON>y danh sách bàn
GET /api/orders - L<PERSON>y tất cả đơn hàng  
GET /api/orders?from_date=YYYY-MM-DD&to_date=YYYY-MM-DD - Đơn hàng theo ngày
GET /api/users - Lấy danh sách người dùng
GET /api/menu/foods - Lấy danh sách món ăn
GET /api/kitchen/stats - Thống kê nhà bếp
GET /api/inventory/low-stock/list - Nguyên liệu sắp hết
GET /api/images/storage/stats - Thống kê storage (chỉ admin)
```

### 4. Các vấn đề có thể gặp:

#### A. Dữ liệu bàn trống = 0
- Kiểm tra `table.status` có đúng là `'Trống'` không
- Có thể status là `'Empty'` hoặc giá trị khác

#### B. Doanh thu = 0đ  
- Kiểm tra có đơn hàng nào có `status = 'Đã thanh toán'` không
- Kiểm tra field `total` có giá trị không
- Kiểm tra filter ngày có đúng không

#### C. Storage stats = 0
- Chỉ admin mới có quyền truy cập
- Kiểm tra có file ảnh trong thư mục uploads không

#### D. Tổng số đơn hàng/người dùng = 0
- Kiểm tra database có dữ liệu không
- Kiểm tra API có trả về data không

### 5. Cách sửa nhanh:

#### Nếu status bàn không đúng:
```javascript
// Thay vì 'Trống', có thể cần dùng:
const emptyTables = tables.filter(table => 
  table.status === 'Available' || 
  table.status === 'Empty' || 
  table.status === 'Trống'
).length;
```

#### Nếu không có dữ liệu test:
1. Tạo một vài bàn trong admin
2. Tạo một vài đơn hàng test
3. Thay đổi status một số đơn hàng thành 'Đã thanh toán'

### 6. Script kiểm tra dữ liệu:

Chạy trong Console của browser (F12):
```javascript
// Kiểm tra token
console.log('Token:', localStorage.getItem('token'));

// Kiểm tra API trực tiếp
fetch('/api/tables', {
  headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
})
.then(r => r.json())
.then(data => console.log('Tables:', data));

fetch('/api/orders', {
  headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
})
.then(r => r.json())
.then(data => console.log('Orders:', data));
```

### 7. Kiểm tra database trực tiếp:

Nếu có quyền truy cập database, chạy các query:
```sql
-- Kiểm tra bàn
SELECT name, status FROM tables;

-- Kiểm tra đơn hàng
SELECT id, status, total, order_time FROM orders;

-- Kiểm tra người dùng  
SELECT username, role_id FROM users;

-- Kiểm tra món ăn
SELECT COUNT(*) as total_foods FROM foods;
```

## Kết luận:

Sau khi debug, cập nhật file này với kết quả tìm được và cách sửa cụ thể.
