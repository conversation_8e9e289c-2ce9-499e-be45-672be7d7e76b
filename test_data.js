const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:3000'; // API Gateway port
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// Hàm đăng nhập để lấy token admin
async function loginAdmin() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
      username: ADMIN_USERNAME,
      password: ADMIN_PASSWORD
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Lỗi đăng nhập admin:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm kiểm tra dữ liệu hiện tại
async function checkCurrentData() {
  try {
    console.log('🔍 Kiểm tra dữ liệu hiện tại...\n');
    
    // Đăng nhập admin để lấy token
    const token = await loginAdmin();
    console.log('✅ Đăng nhập admin thành công!\n');
    
    // Kiểm tra bàn
    console.log('📋 Kiểm tra dữ liệu bàn:');
    const tablesResponse = await axios.get(`${API_BASE_URL}/api/tables`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const tables = tablesResponse.data;
    console.log(`   - Tổng số bàn: ${tables.length}`);
    console.log(`   - Bàn đang phục vụ: ${tables.filter(t => t.status === 'Đang phục vụ').length}`);
    console.log(`   - Bàn trống: ${tables.filter(t => t.status === 'Trống').length}`);
    
    // In ra một vài bàn để kiểm tra
    console.log('   - Một số bàn mẫu:');
    tables.slice(0, 3).forEach(table => {
      console.log(`     * ${table.name}: ${table.status}`);
    });
    
    // Kiểm tra đơn hàng
    console.log('\n📦 Kiểm tra dữ liệu đơn hàng:');
    const ordersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const orders = ordersResponse.data;
    console.log(`   - Tổng số đơn hàng: ${orders.length}`);
    console.log(`   - Đơn hàng đã thanh toán: ${orders.filter(o => o.status === 'Đã thanh toán').length}`);
    console.log(`   - Đơn hàng đang phục vụ: ${orders.filter(o => o.status === 'Đang phục vụ').length}`);
    
    // Kiểm tra đơn hàng hôm nay
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    const todayOrdersResponse = await axios.get(`${API_BASE_URL}/api/orders?from_date=${today}&to_date=${tomorrowStr}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const todayOrders = todayOrdersResponse.data;
    console.log(`   - Đơn hàng hôm nay: ${todayOrders.length}`);
    
    // Tính doanh thu hôm nay
    const todayPaidOrders = todayOrders.filter(o => o.status === 'Đã thanh toán');
    const todayRevenue = todayPaidOrders.reduce((total, order) => total + (order.total || 0), 0);
    console.log(`   - Doanh thu hôm nay: ${todayRevenue.toLocaleString('vi-VN')} VNĐ`);
    
    // Kiểm tra người dùng
    console.log('\n👥 Kiểm tra dữ liệu người dùng:');
    const usersResponse = await axios.get(`${API_BASE_URL}/api/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const users = usersResponse.data;
    console.log(`   - Tổng số người dùng: ${users.length}`);
    console.log(`   - Admin: ${users.filter(u => u.role_id === 1).length}`);
    console.log(`   - Nhân viên: ${users.filter(u => u.role_id === 2).length}`);
    console.log(`   - Đầu bếp: ${users.filter(u => u.role_id === 3).length}`);
    
    // Kiểm tra món ăn
    console.log('\n🍽️ Kiểm tra dữ liệu món ăn:');
    const foodsResponse = await axios.get(`${API_BASE_URL}/api/menu/foods`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const foods = foodsResponse.data;
    console.log(`   - Tổng số món ăn: ${foods.length}`);
    
    // Kiểm tra nhà bếp
    console.log('\n🔥 Kiểm tra dữ liệu nhà bếp:');
    const kitchenResponse = await axios.get(`${API_BASE_URL}/api/kitchen/stats`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const kitchenStats = kitchenResponse.data;
    console.log(`   - Món đang chờ: ${kitchenStats.pending_count}`);
    console.log(`   - Món đang chế biến: ${kitchenStats.cooking_count}`);
    console.log(`   - Món hoàn thành: ${kitchenStats.completed_count}`);
    
    // Kiểm tra kho
    console.log('\n📦 Kiểm tra dữ liệu kho:');
    const inventoryResponse = await axios.get(`${API_BASE_URL}/api/inventory/low-stock/list`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const lowStock = inventoryResponse.data;
    console.log(`   - Nguyên liệu sắp hết: ${lowStock.length}`);
    
    // Kiểm tra storage (nếu có quyền)
    try {
      console.log('\n🖼️ Kiểm tra dữ liệu storage:');
      const storageResponse = await axios.get(`${API_BASE_URL}/api/images/storage/stats`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const storageStats = storageResponse.data;
      console.log(`   - Ảnh món ăn: ${storageStats.foods.total} files (${storageStats.foods.sizeMB} MB)`);
      console.log(`   - Ảnh người dùng: ${storageStats.users.total} files (${storageStats.users.sizeMB} MB)`);
      console.log(`   - File tạm: ${storageStats.temp.total} files (${storageStats.temp.sizeMB} MB)`);
    } catch (error) {
      console.log('   - Không thể truy cập thống kê storage (có thể do quyền hạn)');
    }
    
    console.log('\n✅ Kiểm tra dữ liệu hoàn thành!');
    
  } catch (error) {
    console.error('💥 Lỗi khi kiểm tra dữ liệu:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Chạy script
if (require.main === module) {
  checkCurrentData();
}

module.exports = { checkCurrentData };
