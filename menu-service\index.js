require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.MENU_SERVICE_PORT || 3002;

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Dữ liệu mẫu cho buffet packages
const buffetPackages = [
  {
    id: 1,
    name: "Buffet Cơ Bản",
    description: "Ăn thoải mái tất cả món ăn",
    price: 299000,
    icon: "fas fa-utensils",
    color: "primary",
    features: [
      "Tất cả món ăn trong menu",
      "Nước uống miễn phí",
      "Thời gian không giới hạn",
      "Phục vụ tận bàn"
    ],
    popular: false
  },
  {
    id: 2,
    name: "Buffet Premium",
    description: "Trải nghiệm ẩm thực cao cấp",
    price: 499000,
    icon: "fas fa-crown",
    color: "warning",
    features: [
      "Tất cả món ăn + món đặc biệt",
      "Hải sản tươi sống",
      "Rượu vang và cocktail",
      "Dessert cao cấp",
      "Phục vụ riêng biệt"
    ],
    popular: true
  },
  {
    id: 3,
    name: "Buffet Gia Đình",
    description: "Hoàn hảo cho cả gia đình",
    price: 199000,
    icon: "fas fa-home",
    color: "success",
    features: [
      "Menu thân thiện với trẻ em",
      "Khu vui chơi trẻ em",
      "Giảm giá cho trẻ dưới 12 tuổi",
      "Không gian rộng rãi",
      "Parking miễn phí"
    ],
    popular: false
  },
  {
    id: 4,
    name: "Buffet Chay",
    description: "Thực đơn chay đa dạng và bổ dưỡng",
    price: 249000,
    icon: "fas fa-leaf",
    color: "success",
    features: [
      "100% thực đơn chay",
      "Nguyên liệu organic",
      "Món ăn healthy",
      "Nước ép trái cây tươi",
      "Không MSG"
    ],
    popular: false
  },
  {
    id: 5,
    name: "Buffet Hải Sản",
    description: "Tiệc hải sản tươi ngon",
    price: 699000,
    icon: "fas fa-fish",
    color: "info",
    features: [
      "Hải sản tươi sống hàng ngày",
      "Tôm hùm, cua king",
      "Sashimi và sushi",
      "Nướng BBQ hải sản",
      "Rượu sake premium"
    ],
    popular: false
  },
  {
    id: 6,
    name: "Buffet Lẩu",
    description: "Lẩu thả ga với nhiều loại nước dùng",
    price: 349000,
    icon: "fas fa-fire",
    color: "danger",
    features: [
      "5 loại nước lẩu khác nhau",
      "Thịt bò Wagyu",
      "Hải sản tươi sống",
      "Rau củ organic",
      "Kem và tráng miệng"
    ],
    popular: false
  },
  {
    id: 7,
    name: "Buffet BBQ",
    description: "Nướng không giới hạn",
    price: 399000,
    icon: "fas fa-fire-alt",
    color: "dark",
    features: [
      "Thịt nướng cao cấp",
      "Hải sản nướng",
      "Salad bar phong phú",
      "Bia tươi không giới hạn",
      "Không gian ngoài trời"
    ],
    popular: false
  },
  {
    id: 8,
    name: "Buffet Tối VIP",
    description: "Trải nghiệm ẩm thực đẳng cấp",
    price: 899000,
    icon: "fas fa-gem",
    color: "purple",
    features: [
      "Menu chef đặc biệt",
      "Rượu vang imported",
      "Phục vụ 1:1",
      "Không gian riêng tư",
      "Live music",
      "Valet parking"
    ],
    popular: false
  }
];

// Routes

// Lấy danh sách tất cả buffet packages
app.get('/api/buffet-packages', async (req, res) => {
  try {
    res.json(buffetPackages);
  } catch (error) {
    console.error('Error fetching buffet packages:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách gói buffet' });
  }
});

// Lấy thông tin một gói buffet cụ thể
app.get('/api/buffet-packages/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const buffetPackage = buffetPackages.find(pkg => pkg.id === parseInt(id));

    if (!buffetPackage) {
      return res.status(404).json({ message: 'Không tìm thấy gói buffet' });
    }

    res.json(buffetPackage);
  } catch (error) {
    console.error('Error fetching buffet package:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin gói buffet' });
  }
});

// Lấy danh sách tất cả các danh mục
app.get('/api/categories', async (req, res) => {
  try {
    const result = await db.executeQuery('SELECT * FROM categories');
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách danh mục' });
  }
});

// Lấy thông tin một danh mục cụ thể
app.get('/api/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await db.executeQuery('SELECT * FROM categories WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin danh mục' });
  }
});

// Tạo danh mục mới
app.post('/api/categories', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tên danh mục là bắt buộc' });
    }

    const result = await db.executeQuery(
      'INSERT INTO categories (name) OUTPUT INSERTED.* VALUES (@name)',
      [{ name: 'name', type: db.sql.NVarChar(100), value: name }]
    );

    res.status(201).json(result.recordset[0]);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ message: 'Lỗi khi tạo danh mục mới' });
  }
});

// Cập nhật thông tin danh mục
app.put('/api/categories/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tên danh mục là bắt buộc' });
    }

    const result = await db.executeQuery(
      'UPDATE categories SET name = @name OUTPUT INSERTED.* WHERE id = @id',
      [
        { name: 'name', type: db.sql.NVarChar(100), value: name },
        { name: 'id', type: db.sql.Int, value: id }
      ]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin danh mục' });
  }
});

// Xóa danh mục
app.delete('/api/categories/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra xem danh mục có món ăn không
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM foods WHERE category_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset[0].count > 0) {
      return res.status(400).json({ message: 'Không thể xóa danh mục có món ăn' });
    }

    const result = await db.executeQuery(
      'DELETE FROM categories OUTPUT DELETED.* WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json({ message: 'Đã xóa danh mục thành công', category: result.recordset[0] });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Lỗi khi xóa danh mục' });
  }
});

// Lấy danh sách tất cả các món ăn
app.get('/api/foods', async (req, res) => {
  try {
    const { category_id } = req.query;

    let query = `
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
    `;

    const params = [];

    if (category_id) {
      query += ' WHERE f.category_id = @categoryId';
      params.push({ name: 'categoryId', type: db.sql.Int, value: category_id });
    }

    const result = await db.executeQuery(query, params);
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching foods:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách món ăn' });
  }
});

// Lấy thông tin một món ăn cụ thể
app.get('/api/foods/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Lấy thông tin món ăn
    const foodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (foodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    const food = foodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    const ingredientsResult = await db.executeQuery(`
      SELECT i.id, i.name, i.unit, fi.amount
      FROM food_ingredients fi
      JOIN ingredients i ON fi.ingredient_id = i.id
      WHERE fi.food_id = @foodId
    `, [{ name: 'foodId', type: db.sql.Int, value: id }]);

    food.ingredients = ingredientsResult.recordset;

    res.json(food);
  } catch (error) {
    console.error('Error fetching food:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin món ăn' });
  }
});

// Tạo món ăn mới
app.post('/api/foods', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  try {
    const { name, price, image_url, category_id, ingredients } = req.body;

    if (!name || !price) {
      return res.status(400).json({ message: 'Tên và giá món ăn là bắt buộc' });
    }

    // Tạo món ăn mới
    const foodResult = await db.executeQuery(`
      INSERT INTO foods (name, price, image_url, category_id)
      OUTPUT INSERTED.*
      VALUES (@name, @price, @image_url, @category_id)
    `, [
      { name: 'name', type: db.sql.NVarChar(255), value: name },
      { name: 'price', type: db.sql.Decimal(10, 2), value: price },
      { name: 'image_url', type: db.sql.NVarChar(db.sql.MAX), value: image_url || null },
      { name: 'category_id', type: db.sql.Int, value: category_id || null }
    ]);

    const newFood = foodResult.recordset[0];

    // Thêm nguyên liệu nếu có
    if (ingredients && ingredients.length > 0) {
      for (const ingredient of ingredients) {
        await db.executeQuery(`
          INSERT INTO food_ingredients (food_id, ingredient_id, amount)
          VALUES (@food_id, @ingredient_id, @amount)
        `, [
          { name: 'food_id', type: db.sql.Int, value: newFood.id },
          { name: 'ingredient_id', type: db.sql.Int, value: ingredient.id },
          { name: 'amount', type: db.sql.Decimal(10, 2), value: ingredient.amount }
        ]);
      }
    }

    // Lấy thông tin đầy đủ của món ăn vừa tạo
    const fullFoodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: newFood.id }]);

    const fullFood = fullFoodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    if (ingredients && ingredients.length > 0) {
      const ingredientsResult = await db.executeQuery(`
        SELECT i.id, i.name, i.unit, fi.amount
        FROM food_ingredients fi
        JOIN ingredients i ON fi.ingredient_id = i.id
        WHERE fi.food_id = @foodId
      `, [{ name: 'foodId', type: db.sql.Int, value: newFood.id }]);

      fullFood.ingredients = ingredientsResult.recordset;
    } else {
      fullFood.ingredients = [];
    }

    res.status(201).json(fullFood);
  } catch (error) {
    console.error('Error creating food:', error);
    res.status(500).json({ message: 'Lỗi khi tạo món ăn mới' });
  }
});

// Cập nhật thông tin món ăn
app.put('/api/foods/:id', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, price, image_url, category_id, ingredients } = req.body;

    if (!name && !price && !image_url && !category_id && !ingredients) {
      return res.status(400).json({ message: 'Cần cung cấp ít nhất một trường để cập nhật' });
    }

    // Kiểm tra xem món ăn có tồn tại không
    const checkFoodResult = await db.executeQuery(
      'SELECT * FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkFoodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    // Cập nhật thông tin món ăn
    let updateQuery = 'UPDATE foods SET ';
    const updateParams = [];

    if (name) {
      updateQuery += 'name = @name, ';
      updateParams.push({ name: 'name', type: db.sql.NVarChar(255), value: name });
    }

    if (price) {
      updateQuery += 'price = @price, ';
      updateParams.push({ name: 'price', type: db.sql.Decimal(10, 2), value: price });
    }

    if (image_url !== undefined) {
      updateQuery += 'image_url = @imageUrl, ';
      updateParams.push({ name: 'imageUrl', type: db.sql.NVarChar(db.sql.MAX), value: image_url });
    }

    if (category_id !== undefined) {
      updateQuery += 'category_id = @categoryId, ';
      updateParams.push({ name: 'categoryId', type: db.sql.Int, value: category_id });
    }

    // Xóa dấu phẩy cuối cùng
    updateQuery = updateQuery.slice(0, -2);

    updateQuery += ' OUTPUT INSERTED.* WHERE id = @id';
    updateParams.push({ name: 'id', type: db.sql.Int, value: id });

    const foodResult = await db.executeQuery(updateQuery, updateParams);

    // Cập nhật nguyên liệu nếu có
    if (ingredients && ingredients.length > 0) {
      // Xóa tất cả nguyên liệu hiện tại
      await db.executeQuery(
        'DELETE FROM food_ingredients WHERE food_id = @foodId',
        [{ name: 'foodId', type: db.sql.Int, value: id }]
      );

      // Thêm nguyên liệu mới
      for (const ingredient of ingredients) {
        await db.executeQuery(`
          INSERT INTO food_ingredients (food_id, ingredient_id, amount)
          VALUES (@food_id, @ingredient_id, @amount)
        `, [
          { name: 'food_id', type: db.sql.Int, value: id },
          { name: 'ingredient_id', type: db.sql.Int, value: ingredient.id },
          { name: 'amount', type: db.sql.Decimal(10, 2), value: ingredient.amount }
        ]);
      }
    }

    // Lấy thông tin đầy đủ của món ăn vừa cập nhật
    const fullFoodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    const fullFood = fullFoodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    const ingredientsResult = await db.executeQuery(`
      SELECT i.id, i.name, i.unit, fi.amount
      FROM food_ingredients fi
      JOIN ingredients i ON fi.ingredient_id = i.id
      WHERE fi.food_id = @foodId
    `, [{ name: 'foodId', type: db.sql.Int, value: id }]);

    fullFood.ingredients = ingredientsResult.recordset;

    res.json(fullFood);
  } catch (error) {
    console.error('Error updating food:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin món ăn' });
  }
});

// Xóa món ăn
app.delete('/api/foods/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;
    const force = req.query.force === 'true';

    // Kiểm tra xem món ăn có trong đơn hàng không
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM order_details WHERE food_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    // Nếu món ăn có trong đơn hàng và không có tham số force=true
    if (checkResult.recordset[0].count > 0 && !force) {
      return res.status(400).json({
        message: 'Không thể xóa món ăn đã có trong đơn hàng',
        canForceDelete: true
      });
    }

    // Kiểm tra xem món ăn có tồn tại không
    const checkFoodResult = await db.executeQuery(
      'SELECT * FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkFoodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    const food = checkFoodResult.recordset[0];

    // Xóa tất cả nguyên liệu của món ăn
    await db.executeQuery(
      'DELETE FROM food_ingredients WHERE food_id = @foodId',
      [{ name: 'foodId', type: db.sql.Int, value: id }]
    );

    // Xóa món ăn
    await db.executeQuery(
      'DELETE FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    res.json({ message: 'Đã xóa món ăn thành công', food });
  } catch (error) {
    console.error('Error deleting food:', error);
    res.status(500).json({ message: 'Lỗi khi xóa món ăn' });
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Khởi động server
app.listen(PORT, () => {
  console.log(`Menu Service đang chạy tại http://localhost:${PORT}`);
});
