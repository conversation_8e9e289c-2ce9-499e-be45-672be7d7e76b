require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.MENU_SERVICE_PORT || 3002;

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Dữ liệu mẫu cho buffet packages
const buffetPackages = [
  {
    id: 1,
    name: "Buffet Cơ Bản",
    description: "Ăn thoải mái tất cả món ăn",
    price: 299000,
    icon: "fas fa-utensils",
    color: "primary",
    drink_tier: "basic",
    features: [
      "Tất cả món ăn trong menu",
      "Nước ngọt & trà đá miễn phí",
      "Thời gian không giới hạn",
      "Phục vụ tận bàn"
    ],
    popular: false
  },
  {
    id: 2,
    name: "Buffet Premium",
    description: "Trải nghiệm ẩm thực cao cấp",
    price: 499000,
    icon: "fas fa-crown",
    color: "warning",
    drink_tier: "all",
    features: [
      "Tất cả món ăn + món đặc biệt",
      "Hải sản tươi sống",
      "Tất cả đồ uống không giới hạn",
      "Dessert cao cấp",
      "Phục vụ riêng biệt"
    ],
    popular: true
  },
  {
    id: 3,
    name: "Buffet Gia Đình",
    description: "Hoàn hảo cho cả gia đình",
    price: 199000,
    icon: "fas fa-home",
    color: "success",
    drink_tier: "basic",
    features: [
      "Menu thân thiện với trẻ em",
      "Nước ngọt & nước ép trái cây",
      "Giảm giá cho trẻ dưới 12 tuổi",
      "Không gian rộng rãi",
      "Parking miễn phí"
    ],
    popular: false
  },
  {
    id: 4,
    name: "Buffet Chay",
    description: "Thực đơn chay đa dạng và bổ dưỡng",
    price: 249000,
    icon: "fas fa-leaf",
    color: "success",
    drink_tier: "basic",
    features: [
      "100% thực đơn chay",
      "Nguyên liệu organic",
      "Nước ép trái cây & trà thảo mộc",
      "Món ăn healthy",
      "Không MSG"
    ],
    popular: false
  },
  {
    id: 5,
    name: "Buffet Hải Sản",
    description: "Tiệc hải sản tươi ngon",
    price: 699000,
    icon: "fas fa-fish",
    color: "info",
    drink_tier: "luxury",
    features: [
      "Hải sản tươi sống hàng ngày",
      "Tôm hùm, cua king",
      "Sashimi và sushi",
      "Rượu sake & vang trắng",
      "Cocktail hải sản đặc biệt"
    ],
    popular: false
  },
  {
    id: 6,
    name: "Buffet Lẩu",
    description: "Lẩu thả ga với nhiều loại nước dùng",
    price: 349000,
    icon: "fas fa-fire",
    color: "danger",
    drink_tier: "premium",
    features: [
      "5 loại nước lẩu khác nhau",
      "Thịt bò Wagyu",
      "Bia & cocktail không cồn",
      "Rau củ organic",
      "Kem và tráng miệng"
    ],
    popular: false
  },
  {
    id: 7,
    name: "Buffet BBQ",
    description: "Nướng không giới hạn",
    price: 399000,
    icon: "fas fa-fire-alt",
    color: "dark",
    drink_tier: "premium",
    features: [
      "Thịt nướng cao cấp",
      "Hải sản nướng",
      "Bia tươi & cocktail không cồn",
      "Salad bar phong phú",
      "Không gian ngoài trời"
    ],
    popular: false
  },
  {
    id: 8,
    name: "Buffet Tối VIP",
    description: "Trải nghiệm ẩm thực đẳng cấp",
    price: 899000,
    icon: "fas fa-gem",
    color: "purple",
    drink_tier: "all",
    features: [
      "Menu chef đặc biệt",
      "Tất cả đồ uống không giới hạn",
      "Phục vụ 1:1",
      "Không gian riêng tư",
      "Live music",
      "Valet parking"
    ],
    popular: false
  }
];

// Routes

// Lấy danh sách tất cả buffet packages
app.get('/api/buffet-packages', async (req, res) => {
  try {
    res.json(buffetPackages);
  } catch (error) {
    console.error('Error fetching buffet packages:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách gói buffet' });
  }
});

// API mới: Lấy món nước miễn phí theo tier cho buffet package
app.get('/api/buffet-packages/:id/free-drinks', async (req, res) => {
  try {
    const { id } = req.params;
    const { tier } = req.query; // 'basic', 'premium', 'luxury', 'all'

    // Định nghĩa tier mapping cho từng gói buffet
    const tierMapping = {
      1: 'basic',     // Buffet Cơ Bản
      2: 'all',       // Buffet Premium
      3: 'basic',     // Buffet Gia Đình
      4: 'basic',     // Buffet Chay
      5: 'luxury',    // Buffet Hải Sản
      6: 'premium',   // Buffet Lẩu
      7: 'premium',   // Buffet BBQ
      8: 'all'        // Buffet Tối VIP
    };

    const packageTier = tier || tierMapping[parseInt(id)] || 'basic';

    // Lấy đồ uống theo tier
    let query = `
      SELECT f.*, c.name as category_name
      FROM foods f
      JOIN categories c ON f.category_id = c.id
      WHERE c.is_drink = 1
    `;

    const params = [];

    // Phân loại theo tier
    if (packageTier === 'basic') {
      query += ` AND f.price <= 50000`; // Nước ngọt, trà đá cơ bản
    } else if (packageTier === 'premium') {
      query += ` AND f.price <= 100000`; // Thêm bia, cocktail không cồn
    } else if (packageTier === 'luxury') {
      query += ` AND f.price <= 200000`; // Thêm rượu vang, cocktail có cồn
    }
    // 'all' không có điều kiện giá

    query += ` ORDER BY f.price, f.name`;

    const result = await db.executeQuery(query, params);

    res.json({
      package_id: parseInt(id),
      tier: packageTier,
      drinks: result.recordset
    });
  } catch (error) {
    console.error('Error fetching free drinks for buffet package:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách món nước miễn phí' });
  }
});

// Lấy thông tin một gói buffet cụ thể với món nước miễn phí
app.get('/api/buffet-packages/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const buffetPackage = buffetPackages.find(pkg => pkg.id === parseInt(id));

    if (!buffetPackage) {
      return res.status(404).json({ message: 'Không tìm thấy gói buffet' });
    }

    // Lấy danh sách món nước miễn phí cho gói buffet này
    try {
      const freeDrinksResult = await db.executeQuery(`
        SELECT f.*, c.name as category_name, bpd.drink_tier
        FROM buffet_package_drinks bpd
        LEFT JOIN foods f ON bpd.drink_id = f.id
        LEFT JOIN categories c ON f.category_id = c.id
        WHERE bpd.buffet_package_id = @packageId
        ORDER BY bpd.drink_tier, f.name
      `, [{ name: 'packageId', type: db.sql.Int, value: parseInt(id) }]);

      buffetPackage.free_drinks = freeDrinksResult.recordset;
    } catch (dbError) {
      console.log('⚠️ Chưa có dữ liệu món nước miễn phí, sử dụng default');
      buffetPackage.free_drinks = [];
    }

    res.json(buffetPackage);
  } catch (error) {
    console.error('Error fetching buffet package:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin gói buffet' });
  }
});

// Lấy danh sách tất cả các danh mục
app.get('/api/categories', async (req, res) => {
  try {
    const result = await db.executeQuery('SELECT * FROM categories');
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách danh mục' });
  }
});

// Lấy thông tin một danh mục cụ thể
app.get('/api/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await db.executeQuery('SELECT * FROM categories WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin danh mục' });
  }
});

// Tạo danh mục mới
app.post('/api/categories', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tên danh mục là bắt buộc' });
    }

    const result = await db.executeQuery(
      'INSERT INTO categories (name) OUTPUT INSERTED.* VALUES (@name)',
      [{ name: 'name', type: db.sql.NVarChar(100), value: name }]
    );

    res.status(201).json(result.recordset[0]);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ message: 'Lỗi khi tạo danh mục mới' });
  }
});

// Cập nhật thông tin danh mục
app.put('/api/categories/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tên danh mục là bắt buộc' });
    }

    const result = await db.executeQuery(
      'UPDATE categories SET name = @name OUTPUT INSERTED.* WHERE id = @id',
      [
        { name: 'name', type: db.sql.NVarChar(100), value: name },
        { name: 'id', type: db.sql.Int, value: id }
      ]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin danh mục' });
  }
});

// Xóa danh mục
app.delete('/api/categories/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra xem danh mục có món ăn không
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM foods WHERE category_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset[0].count > 0) {
      return res.status(400).json({ message: 'Không thể xóa danh mục có món ăn' });
    }

    const result = await db.executeQuery(
      'DELETE FROM categories OUTPUT DELETED.* WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy danh mục' });
    }

    res.json({ message: 'Đã xóa danh mục thành công', category: result.recordset[0] });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Lỗi khi xóa danh mục' });
  }
});

// Lấy danh sách tất cả các món ăn
app.get('/api/foods', async (req, res) => {
  try {
    const { category_id } = req.query;

    let query = `
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
    `;

    const params = [];

    if (category_id) {
      query += ' WHERE f.category_id = @categoryId';
      params.push({ name: 'categoryId', type: db.sql.Int, value: category_id });
    }

    const result = await db.executeQuery(query, params);
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching foods:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách món ăn' });
  }
});

// Lấy thông tin một món ăn cụ thể
app.get('/api/foods/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Lấy thông tin món ăn
    const foodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (foodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    const food = foodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    const ingredientsResult = await db.executeQuery(`
      SELECT i.id, i.name, i.unit, fi.amount
      FROM food_ingredients fi
      JOIN ingredients i ON fi.ingredient_id = i.id
      WHERE fi.food_id = @foodId
    `, [{ name: 'foodId', type: db.sql.Int, value: id }]);

    food.ingredients = ingredientsResult.recordset;

    res.json(food);
  } catch (error) {
    console.error('Error fetching food:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin món ăn' });
  }
});

// Tạo món ăn mới
app.post('/api/foods', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  try {
    const { name, price, image_url, category_id, ingredients } = req.body;

    if (!name || !price) {
      return res.status(400).json({ message: 'Tên và giá món ăn là bắt buộc' });
    }

    // Tạo món ăn mới
    const foodResult = await db.executeQuery(`
      INSERT INTO foods (name, price, image_url, category_id)
      OUTPUT INSERTED.*
      VALUES (@name, @price, @image_url, @category_id)
    `, [
      { name: 'name', type: db.sql.NVarChar(255), value: name },
      { name: 'price', type: db.sql.Decimal(10, 2), value: price },
      { name: 'image_url', type: db.sql.NVarChar(db.sql.MAX), value: image_url || null },
      { name: 'category_id', type: db.sql.Int, value: category_id || null }
    ]);

    const newFood = foodResult.recordset[0];

    // Thêm nguyên liệu nếu có
    if (ingredients && ingredients.length > 0) {
      for (const ingredient of ingredients) {
        await db.executeQuery(`
          INSERT INTO food_ingredients (food_id, ingredient_id, amount)
          VALUES (@food_id, @ingredient_id, @amount)
        `, [
          { name: 'food_id', type: db.sql.Int, value: newFood.id },
          { name: 'ingredient_id', type: db.sql.Int, value: ingredient.id },
          { name: 'amount', type: db.sql.Decimal(10, 2), value: ingredient.amount }
        ]);
      }
    }

    // Lấy thông tin đầy đủ của món ăn vừa tạo
    const fullFoodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: newFood.id }]);

    const fullFood = fullFoodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    if (ingredients && ingredients.length > 0) {
      const ingredientsResult = await db.executeQuery(`
        SELECT i.id, i.name, i.unit, fi.amount
        FROM food_ingredients fi
        JOIN ingredients i ON fi.ingredient_id = i.id
        WHERE fi.food_id = @foodId
      `, [{ name: 'foodId', type: db.sql.Int, value: newFood.id }]);

      fullFood.ingredients = ingredientsResult.recordset;
    } else {
      fullFood.ingredients = [];
    }

    res.status(201).json(fullFood);
  } catch (error) {
    console.error('Error creating food:', error);
    res.status(500).json({ message: 'Lỗi khi tạo món ăn mới' });
  }
});

// Cập nhật thông tin món ăn
app.put('/api/foods/:id', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, price, image_url, category_id, ingredients } = req.body;

    if (!name && !price && !image_url && !category_id && !ingredients) {
      return res.status(400).json({ message: 'Cần cung cấp ít nhất một trường để cập nhật' });
    }

    // Kiểm tra xem món ăn có tồn tại không
    const checkFoodResult = await db.executeQuery(
      'SELECT * FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkFoodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    // Cập nhật thông tin món ăn
    let updateQuery = 'UPDATE foods SET ';
    const updateParams = [];

    if (name) {
      updateQuery += 'name = @name, ';
      updateParams.push({ name: 'name', type: db.sql.NVarChar(255), value: name });
    }

    if (price) {
      updateQuery += 'price = @price, ';
      updateParams.push({ name: 'price', type: db.sql.Decimal(10, 2), value: price });
    }

    if (image_url !== undefined) {
      updateQuery += 'image_url = @imageUrl, ';
      updateParams.push({ name: 'imageUrl', type: db.sql.NVarChar(db.sql.MAX), value: image_url });
    }

    if (category_id !== undefined) {
      updateQuery += 'category_id = @categoryId, ';
      updateParams.push({ name: 'categoryId', type: db.sql.Int, value: category_id });
    }

    // Xóa dấu phẩy cuối cùng
    updateQuery = updateQuery.slice(0, -2);

    updateQuery += ' OUTPUT INSERTED.* WHERE id = @id';
    updateParams.push({ name: 'id', type: db.sql.Int, value: id });

    const foodResult = await db.executeQuery(updateQuery, updateParams);

    // Cập nhật nguyên liệu nếu có
    if (ingredients && ingredients.length > 0) {
      // Xóa tất cả nguyên liệu hiện tại
      await db.executeQuery(
        'DELETE FROM food_ingredients WHERE food_id = @foodId',
        [{ name: 'foodId', type: db.sql.Int, value: id }]
      );

      // Thêm nguyên liệu mới
      for (const ingredient of ingredients) {
        await db.executeQuery(`
          INSERT INTO food_ingredients (food_id, ingredient_id, amount)
          VALUES (@food_id, @ingredient_id, @amount)
        `, [
          { name: 'food_id', type: db.sql.Int, value: id },
          { name: 'ingredient_id', type: db.sql.Int, value: ingredient.id },
          { name: 'amount', type: db.sql.Decimal(10, 2), value: ingredient.amount }
        ]);
      }
    }

    // Lấy thông tin đầy đủ của món ăn vừa cập nhật
    const fullFoodResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      WHERE f.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    const fullFood = fullFoodResult.recordset[0];

    // Lấy thông tin nguyên liệu của món ăn
    const ingredientsResult = await db.executeQuery(`
      SELECT i.id, i.name, i.unit, fi.amount
      FROM food_ingredients fi
      JOIN ingredients i ON fi.ingredient_id = i.id
      WHERE fi.food_id = @foodId
    `, [{ name: 'foodId', type: db.sql.Int, value: id }]);

    fullFood.ingredients = ingredientsResult.recordset;

    res.json(fullFood);
  } catch (error) {
    console.error('Error updating food:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin món ăn' });
  }
});

// Xóa món ăn
app.delete('/api/foods/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;
    const force = req.query.force === 'true';

    // Kiểm tra xem món ăn có trong đơn hàng không
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM order_details WHERE food_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    // Nếu món ăn có trong đơn hàng và không có tham số force=true
    if (checkResult.recordset[0].count > 0 && !force) {
      return res.status(400).json({
        message: 'Không thể xóa món ăn đã có trong đơn hàng',
        canForceDelete: true
      });
    }

    // Kiểm tra xem món ăn có tồn tại không
    const checkFoodResult = await db.executeQuery(
      'SELECT * FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkFoodResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn' });
    }

    const food = checkFoodResult.recordset[0];

    // Xóa tất cả nguyên liệu của món ăn
    await db.executeQuery(
      'DELETE FROM food_ingredients WHERE food_id = @foodId',
      [{ name: 'foodId', type: db.sql.Int, value: id }]
    );

    // Xóa món ăn
    await db.executeQuery(
      'DELETE FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    res.json({ message: 'Đã xóa món ăn thành công', food });
  } catch (error) {
    console.error('Error deleting food:', error);
    res.status(500).json({ message: 'Lỗi khi xóa món ăn' });
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Khởi tạo dữ liệu mẫu cho đồ uống
async function initializeDrinkData() {
  try {
    console.log('🍹 Khởi tạo dữ liệu đồ uống...');

    // Kiểm tra xem đã có category đồ uống chưa
    const drinkCategoryResult = await db.executeQuery(`
      SELECT * FROM categories WHERE name LIKE N'%Đồ uống%' OR name LIKE N'%Drinks%'
    `);

    let drinkCategoryId;

    if (drinkCategoryResult.recordset.length === 0) {
      // Tạo category đồ uống mới
      const newCategoryResult = await db.executeQuery(`
        INSERT INTO categories (name, is_drink)
        OUTPUT INSERTED.*
        VALUES (N'Đồ uống (Beverages)', 1)
      `);
      drinkCategoryId = newCategoryResult.recordset[0].id;
      console.log('✅ Đã tạo category đồ uống mới:', drinkCategoryId);
    } else {
      drinkCategoryId = drinkCategoryResult.recordset[0].id;
      // Cập nhật is_drink = 1 cho category này
      await db.executeQuery(`
        UPDATE categories SET is_drink = 1 WHERE id = @id
      `, [{ name: 'id', type: db.sql.Int, value: drinkCategoryId }]);
      console.log('✅ Đã cập nhật category đồ uống:', drinkCategoryId);
    }

    // Kiểm tra xem đã có đồ uống nào chưa
    const existingDrinksResult = await db.executeQuery(`
      SELECT COUNT(*) as count FROM foods WHERE category_id = @categoryId
    `, [{ name: 'categoryId', type: db.sql.Int, value: drinkCategoryId }]);

    if (existingDrinksResult.recordset[0].count === 0) {
      // Thêm một số đồ uống mẫu
      const sampleDrinks = [
        { name: 'Coca Cola', price: 25000, tier: 'basic' },
        { name: 'Pepsi', price: 25000, tier: 'basic' },
        { name: 'Sprite', price: 25000, tier: 'basic' },
        { name: 'Trà đá', price: 15000, tier: 'basic' },
        { name: 'Nước cam ép', price: 35000, tier: 'basic' },
        { name: 'Nước chanh', price: 30000, tier: 'basic' },
        { name: 'Bia Heineken', price: 65000, tier: 'premium' },
        { name: 'Bia Tiger', price: 55000, tier: 'premium' },
        { name: 'Cocktail Mojito', price: 85000, tier: 'premium' },
        { name: 'Rượu vang đỏ', price: 150000, tier: 'luxury' },
        { name: 'Whisky', price: 200000, tier: 'luxury' },
        { name: 'Champagne', price: 300000, tier: 'luxury' }
      ];

      for (const drink of sampleDrinks) {
        await db.executeQuery(`
          INSERT INTO foods (name, price, category_id)
          VALUES (@name, @price, @categoryId)
        `, [
          { name: 'name', type: db.sql.NVarChar(255), value: drink.name },
          { name: 'price', type: db.sql.Decimal(10, 2), value: drink.price },
          { name: 'categoryId', type: db.sql.Int, value: drinkCategoryId }
        ]);
      }

      console.log('✅ Đã thêm', sampleDrinks.length, 'đồ uống mẫu');
    }

    // Sửa lại category id=2 không phải là đồ uống
    await db.executeQuery(`
      UPDATE categories SET is_drink = 0 WHERE id = 2
    `);
    console.log('✅ Đã sửa category id=2 không phải đồ uống');

  } catch (error) {
    console.error('❌ Lỗi khởi tạo dữ liệu đồ uống:', error);
  }
}

// Khởi động server
app.listen(PORT, async () => {
  console.log(`Menu Service đang chạy tại http://localhost:${PORT}`);
  await initializeDrinkData();
});
