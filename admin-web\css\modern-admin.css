/* ==================== MODERN ADMIN DASHBOARD ==================== */

/* Base Layout */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--gray-50);
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-xl);
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-700);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-height: var(--header-height);
}

.sidebar-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  flex-shrink: 0;
}

.sidebar-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-nav {
  padding: var(--space-4) 0;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  padding: 0 var(--space-6) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  color: var(--gray-300);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border-left: 3px solid transparent;
  position: relative;
}

.nav-item:hover {
  background-color: var(--gray-700);
  color: white;
  border-left-color: var(--primary-500);
}

.nav-item.active {
  background-color: var(--primary-600);
  color: white;
  border-left-color: var(--primary-400);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--space-3);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .nav-item-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar.collapsed .nav-item {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
  justify-content: center;
}

/* Main Content Area */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.expanded {
  margin-left: var(--sidebar-width-collapsed);
}

/* Header */
.header {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--space-6);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.user-menu:hover {
  background-color: var(--gray-100);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.2;
}

/* Content Area */
.content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

.page {
  display: none;
}

.page.active {
  display: block;
}

/* Stats Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-card-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: white;
  flex-shrink: 0;
}

.stat-icon.primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.stat-icon.success {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.stat-icon.warning {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.stat-icon.error {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  margin-bottom: var(--space-1);
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  line-height: 1;
}

.stat-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.stat-change.positive {
  color: var(--success-600);
}

.stat-change.negative {
  color: var(--error-600);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: var(--sidebar-width);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .content {
    padding: var(--space-4);
  }
  
  .header {
    padding: 0 var(--space-4);
  }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  display: none;
}

.sidebar-overlay.active {
  display: block;
}

/* Mobile Toggle Button */
.mobile-toggle {
  display: none;
}

@media (max-width: 768px) {
  .mobile-toggle {
    display: flex;
  }
}
