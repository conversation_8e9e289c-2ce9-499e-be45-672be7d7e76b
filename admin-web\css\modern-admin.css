/* ==================== MODERN ADMIN DASHBOARD ==================== */

/* Base Layout */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-xl);
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-700);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-height: var(--header-height);
}

.sidebar-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  flex-shrink: 0;
}

.sidebar-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-nav {
  padding: var(--space-4) 0;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  padding: 0 var(--space-6) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  color: var(--gray-300);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border-left: 3px solid transparent;
  position: relative;
}

.nav-item:hover {
  background-color: var(--gray-700);
  color: white;
  border-left-color: var(--primary-500);
}

.nav-item.active {
  background-color: var(--primary-600);
  color: white;
  border-left-color: var(--primary-400);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--space-3);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: opacity var(--transition-base);
}

.sidebar.collapsed .nav-item-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar.collapsed .nav-item {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
  justify-content: center;
}

/* Main Content Area */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.expanded {
  margin-left: var(--sidebar-width-collapsed);
}

/* Header */
.header {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--space-6);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.user-menu:hover {
  background-color: var(--gray-100);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.2;
}

/* Content Area */
.content {
  flex: 1;
  padding: var(--space-8);
  overflow-y: auto;
  background: transparent;
}

.page {
  display: none;
}

.page.active {
  display: block;
}

/* Stats Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.stat-card {
  background: white;
  border-radius: var(--radius-3xl);
  padding: var(--space-8);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600), var(--primary-700));
  border-radius: var(--radius-3xl) var(--radius-3xl) 0 0;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: var(--primary-200);
}

.stat-card-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-6);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: white;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-2xl);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8, #1e40af);
}

.stat-icon.success {
  background: linear-gradient(135deg, #10b981, #059669, #047857);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
}

.stat-icon.error {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--space-3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1.2;
}

.stat-value {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gray-900);
  line-height: 1;
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  width: fit-content;
}

.stat-change.positive {
  color: var(--success-700);
  background-color: var(--success-50);
  border: 1px solid var(--success-200);
}

.stat-change.negative {
  color: var(--error-700);
  background-color: var(--error-50);
  border: 1px solid var(--error-200);
}

.stat-change.neutral {
  color: var(--gray-700);
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
}

/* Enhanced Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-200);
}

.modern-table th {
  background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
  padding: var(--space-5) var(--space-6);
  text-align: left;
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--gray-200);
  position: relative;
}

.modern-table th:first-child {
  border-top-left-radius: var(--radius-2xl);
}

.modern-table th:last-child {
  border-top-right-radius: var(--radius-2xl);
}

.modern-table td {
  padding: var(--space-5) var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  transition: background-color var(--transition-fast);
}

.modern-table tbody tr:hover {
  background-color: var(--primary-25);
}

.modern-table tbody tr:hover td {
  color: var(--gray-900);
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.modern-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-2xl);
}

.modern-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-2xl);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.success {
  background-color: var(--success-100);
  color: var(--success-800);
  border: 1px solid var(--success-200);
}

.status-badge.warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
  border: 1px solid var(--warning-200);
}

.status-badge.error {
  background-color: var(--error-100);
  color: var(--error-800);
  border: 1px solid var(--error-200);
}

.status-badge.info {
  background-color: var(--primary-100);
  color: var(--primary-800);
  border: 1px solid var(--primary-200);
}

/* Enhanced Cards */
.enhanced-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-3xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-10);
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: var(--radius-3xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.page-header h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: var(--sidebar-width);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .content {
    padding: var(--space-4);
  }
  
  .header {
    padding: 0 var(--space-4);
  }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  display: none;
}

.sidebar-overlay.active {
  display: block;
}

/* Mobile Toggle Button */
.mobile-toggle {
  display: none;
}

@media (max-width: 768px) {
  .mobile-toggle {
    display: flex;
  }
}
