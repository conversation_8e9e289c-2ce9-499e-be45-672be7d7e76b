require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const http = require('http');
const socketIo = require('socket.io');
const axios = require('axios');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

const PORT = process.env.ORDER_SERVICE_PORT || 3003;

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Socket.IO
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('join_table', (tableId) => {
    socket.join(`table_${tableId}`);
    console.log(`Client ${socket.id} joined table ${tableId}`);
  });

  socket.on('leave_table', (tableId) => {
    socket.leave(`table_${tableId}`);
    console.log(`Client ${socket.id} left table ${tableId}`);
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Routes

// Lấy giá buffet
app.get('/api/buffet-price', async (req, res) => {
  try {
    // Giá buffet cố định - có thể lưu trong database sau này
    const buffetPrice = 299000; // 299,000 VND per person
    res.json({
      price: buffetPrice,
      formatted_price: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(buffetPrice) + '/người'
    });
  } catch (error) {
    console.error('Error fetching buffet price:', error);
    res.status(500).json({ message: 'Lỗi khi lấy giá buffet' });
  }
});

// API mới: Tạo đơn hàng buffet với món nước miễn phí
app.post('/api/buffet-orders', async (req, res) => {
  try {
    console.log('Nhận yêu cầu tạo đơn hàng buffet:', req.body);
    const { table_id, table_key, free_drink_id, buffet_price, buffet_session_id, buffet_package_id, buffet_package_name, user_id } = req.body;

    if (!table_id || !free_drink_id || !buffet_session_id) {
      return res.status(400).json({ message: 'Thiếu thông tin bàn, món nước miễn phí hoặc session ID' });
    }

    // Kiểm tra key hợp lệ nếu có
    if (table_key) {
      const keyResult = await db.executeQuery(`
        SELECT * FROM table_keys
        WHERE key_value = @keyValue AND table_id = @tableId AND is_valid = 1 AND expires_at > GETDATE()
      `, [
        { name: 'keyValue', type: db.sql.NVarChar(100), value: table_key },
        { name: 'tableId', type: db.sql.Int, value: table_id }
      ]);

      if (keyResult.recordset.length === 0) {
        return res.status(401).json({ message: 'Key không hợp lệ hoặc đã hết hạn' });
      }
    }

    // Tính tổng tiền buffet
    const total = buffet_price || 299000;

    // Cập nhật trạng thái bàn thành buffet
    await db.executeQuery(`
      UPDATE tables SET is_buffet = 1, status = N'Đang phục vụ' WHERE id = @tableId
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id }
    ]);

    // Tạo đơn hàng buffet
    const orderResult = await db.executeQuery(`
      INSERT INTO orders (table_id, user_id, order_time, status, total, table_key, buffet_session_id, is_buffet, buffet_package_id, buffet_package_name)
      OUTPUT INSERTED.*
      VALUES (@tableId, @userId, GETDATE(), N'Đang phục vụ', @total, @tableKey, @buffetSessionId, 1, @buffetPackageId, @buffetPackageName)
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id },
      { name: 'userId', type: db.sql.Int, value: user_id || null },
      { name: 'total', type: db.sql.Decimal(18, 2), value: total },
      { name: 'tableKey', type: db.sql.NVarChar(100), value: table_key || null },
      { name: 'buffetSessionId', type: db.sql.NVarChar(100), value: buffet_session_id },
      { name: 'buffetPackageId', type: db.sql.Int, value: buffet_package_id || null },
      { name: 'buffetPackageName', type: db.sql.NVarChar(255), value: buffet_package_name || null }
    ]);

    const newOrder = orderResult.recordset[0];

    // Thêm item buffet
    const buffetDisplayName = buffet_package_name ? `${buffet_package_name} - Buffet` : 'Buffet - Ăn thoải mái tất cả món ăn';
    await db.executeQuery(`
      INSERT INTO order_details (order_id, food_id, quantity, price, custom_name)
      VALUES (@orderId, NULL, 1, @price, @customName)
    `, [
      { name: 'orderId', type: db.sql.Int, value: newOrder.id },
      { name: 'price', type: db.sql.Decimal(10, 2), value: total },
      { name: 'customName', type: db.sql.NVarChar(255), value: buffetDisplayName }
    ]);

    // Thêm món nước miễn phí
    const drinkResult = await db.executeQuery(
      'SELECT price FROM foods WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: free_drink_id }]
    );

    if (drinkResult.recordset.length > 0) {
      const drinkDetailResult = await db.executeQuery(`
        INSERT INTO order_details (order_id, food_id, quantity, price, is_free_drink)
        OUTPUT INSERTED.*
        VALUES (@orderId, @foodId, 1, 0, 1)
      `, [
        { name: 'orderId', type: db.sql.Int, value: newOrder.id },
        { name: 'foodId', type: db.sql.Int, value: free_drink_id }
      ]);

      // Thêm món nước miễn phí vào kitchen queue
      await db.executeQuery(`
        INSERT INTO kitchen_queue (order_detail_id, status, updated_at)
        VALUES (@orderDetailId, N'Chờ chế biến', GETDATE())
      `, [
        { name: 'orderDetailId', type: db.sql.Int, value: drinkDetailResult.recordset[0].id }
      ]);
    }

    // Lấy thông tin đầy đủ của đơn hàng
    const fullOrderResult = await db.executeQuery(`
      SELECT o.*, t.name as table_name, u.username as user_username
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: newOrder.id }]);

    const fullOrder = fullOrderResult.recordset[0];

    // Lấy chi tiết đơn hàng
    const detailsResult = await db.executeQuery(`
      SELECT od.*,
             COALESCE(od.custom_name, f.name) as food_name,
             f.image_url as food_image,
             od.is_free_drink
      FROM order_details od
      LEFT JOIN foods f ON od.food_id = f.id
      WHERE od.order_id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: newOrder.id }]);

    fullOrder.details = detailsResult.recordset;

    res.status(201).json(fullOrder);
  } catch (error) {
    console.error('Error creating buffet order:', error);
    res.status(500).json({ message: 'Lỗi khi tạo đơn hàng buffet' });
  }
});

// API mới: Thêm món ăn cho buffet
app.post('/api/buffet-add-food', async (req, res) => {
  try {
    console.log('Nhận yêu cầu thêm món ăn cho buffet:', req.body);
    const { table_id, table_key, items } = req.body;

    if (!table_id || !items || items.length === 0) {
      return res.status(400).json({ message: 'Thiếu thông tin bàn hoặc món ăn' });
    }

    // Kiểm tra key hợp lệ
    if (table_key) {
      const keyResult = await db.executeQuery(`
        SELECT * FROM table_keys
        WHERE key_value = @keyValue AND table_id = @tableId AND is_valid = 1 AND expires_at > GETDATE()
      `, [
        { name: 'keyValue', type: db.sql.NVarChar(100), value: table_key },
        { name: 'tableId', type: db.sql.Int, value: table_id }
      ]);

      if (keyResult.recordset.length === 0) {
        return res.status(401).json({ message: 'Key không hợp lệ hoặc đã hết hạn' });
      }
    }

    // Tìm đơn hàng buffet hiện tại của bàn
    const buffetOrderResult = await db.executeQuery(`
      SELECT TOP 1 * FROM orders
      WHERE table_id = @tableId
        AND table_key = @tableKey
        AND buffet_session_id IS NOT NULL
        AND status NOT IN ('Đã thanh toán', 'Hoàn thành', 'Đã hoàn thành')
      ORDER BY order_time DESC
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id },
      { name: 'tableKey', type: db.sql.NVarChar(100), value: table_key || null }
    ]);

    if (buffetOrderResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng buffet hiện tại' });
    }

    const buffetOrder = buffetOrderResult.recordset[0];

    // Thêm món ăn vào đơn hàng buffet (giá = 0 vì đã trả tiền buffet)
    const addedItems = [];
    for (const item of items) {
      // Kiểm tra món ăn tồn tại và không phải đồ uống
      const foodResult = await db.executeQuery(`
        SELECT f.*, c.is_drink
        FROM foods f
        LEFT JOIN categories c ON f.category_id = c.id
        WHERE f.id = @id
      `, [{ name: 'id', type: db.sql.Int, value: item.food_id }]);

      if (foodResult.recordset.length === 0) {
        return res.status(404).json({ message: `Không tìm thấy món ăn với ID ${item.food_id}` });
      }

      const food = foodResult.recordset[0];
      if (food.is_drink) {
        return res.status(400).json({ message: `Món ${food.name} là đồ uống, không thể thêm vào buffet` });
      }

      // Thêm món ăn với giá 0 (đã trả tiền buffet)
      const detailResult = await db.executeQuery(`
        INSERT INTO order_details (order_id, food_id, quantity, price)
        OUTPUT INSERTED.*
        VALUES (@orderId, @foodId, @quantity, 0)
      `, [
        { name: 'orderId', type: db.sql.Int, value: buffetOrder.id },
        { name: 'foodId', type: db.sql.Int, value: item.food_id },
        { name: 'quantity', type: db.sql.Int, value: item.quantity }
      ]);

      addedItems.push(detailResult.recordset[0]);

      // Thêm vào kitchen queue
      await db.executeQuery(`
        INSERT INTO kitchen_queue (order_detail_id, status, updated_at)
        VALUES (@orderDetailId, N'Chờ chế biến', GETDATE())
      `, [
        { name: 'orderDetailId', type: db.sql.Int, value: detailResult.recordset[0].id }
      ]);
    }

    // Lấy thông tin đầy đủ của đơn hàng
    const fullOrderResult = await db.executeQuery(`
      SELECT o.*, t.name as table_name, u.username as user_username
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: buffetOrder.id }]);

    const fullOrder = fullOrderResult.recordset[0];

    // Lấy chi tiết đơn hàng
    const detailsResult = await db.executeQuery(`
      SELECT od.*,
             COALESCE(od.custom_name, f.name) as food_name,
             f.image_url as food_image,
             od.is_free_drink
      FROM order_details od
      LEFT JOIN foods f ON od.food_id = f.id
      WHERE od.order_id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: buffetOrder.id }]);

    fullOrder.details = detailsResult.recordset;
    fullOrder.added_items = addedItems;

    res.json(fullOrder);
  } catch (error) {
    console.error('Error adding food to buffet:', error);
    res.status(500).json({ message: 'Lỗi khi thêm món ăn cho buffet' });
  }
});

// Lấy danh sách tất cả các đơn hàng
app.get('/api/orders', auth.authenticateToken, async (req, res) => {
  try {
    const { table_id, status, from_date, to_date } = req.query;

    let query = `
      SELECT o.*, t.name as table_name, u.username as user_username
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE 1=1
    `;

    const params = [];

    if (table_id) {
      query += ' AND o.table_id = @tableId';
      params.push({ name: 'tableId', type: db.sql.Int, value: table_id });
    }

    if (status) {
      query += ' AND o.status = @status';
      params.push({ name: 'status', type: db.sql.NVarChar(20), value: status });
    }

    if (from_date) {
      query += ' AND o.order_time >= @fromDate';
      params.push({ name: 'fromDate', type: db.sql.DateTime, value: new Date(from_date) });
    }

    if (to_date) {
      query += ' AND o.order_time <= @toDate';
      params.push({ name: 'toDate', type: db.sql.DateTime, value: new Date(to_date) });
    }

    query += ' ORDER BY o.order_time DESC';

    const result = await db.executeQuery(query, params);
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách đơn hàng' });
  }
});

// Lấy thông tin một đơn hàng cụ thể
app.get('/api/orders/:id', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Lấy thông tin đơn hàng
    const orderResult = await db.executeQuery(`
      SELECT o.*, t.name as table_name, u.username as user_username
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (orderResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    const order = orderResult.recordset[0];

    // Lấy chi tiết đơn hàng
    const detailsResult = await db.executeQuery(`
      SELECT od.*,
             COALESCE(od.custom_name, f.name) as food_name,
             f.image_url as food_image
      FROM order_details od
      LEFT JOIN foods f ON od.food_id = f.id
      WHERE od.order_id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: id }]);

    order.details = detailsResult.recordset;

    res.json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin đơn hàng' });
  }
});

// Tạo đơn hàng mới
app.post('/api/orders', async (req, res) => {
  try {
    console.log('Nhận yêu cầu tạo đơn hàng:', req.body);
    const { table_id, user_id, items, table_key, is_buffet, buffet_price } = req.body;

    if (!table_id || !items || items.length === 0) {
      return res.status(400).json({ message: 'ID bàn và danh sách món ăn là bắt buộc' });
    }

    // Kiểm tra key của bàn nếu có (bỏ qua việc kiểm tra hết hạn)
    if (table_key) {
      const keyResult = await db.executeQuery(
        'SELECT * FROM table_keys WHERE key_value = @key AND table_id = @tableId AND is_valid = 1',
        [
          { name: 'key', type: db.sql.NVarChar(100), value: table_key },
          { name: 'tableId', type: db.sql.Int, value: table_id }
        ]
      );

      console.log('Kiểm tra key:', table_key, 'Kết quả:', keyResult.recordset);

      if (keyResult.recordset.length === 0) {
        return res.status(403).json({ message: 'Key không hợp lệ' });
      }
    } else if (!req.user) {
      // Nếu không có key và không có user (không xác thực) thì không cho phép
      return res.status(401).json({ message: 'Cần xác thực hoặc cung cấp key hợp lệ' });
    }

    // Tính tổng tiền
    let total = 0;

    if (is_buffet) {
      // Nếu là buffet, sử dụng giá buffet cố định
      total = buffet_price || 299000; // Giá buffet mặc định 299,000 VND
    } else {
      // Nếu là đặt món thông thường, tính theo từng món
      for (const item of items) {
        const foodResult = await db.executeQuery(
          'SELECT price FROM foods WHERE id = @id',
          [{ name: 'id', type: db.sql.Int, value: item.food_id }]
        );

        if (foodResult.recordset.length === 0) {
          return res.status(404).json({ message: `Không tìm thấy món ăn với ID ${item.food_id}` });
        }

        total += foodResult.recordset[0].price * item.quantity;
      }
    }

    // Tạo đơn hàng mới
    const orderResult = await db.executeQuery(`
      INSERT INTO orders (table_id, user_id, order_time, status, total, table_key, is_buffet)
      OUTPUT INSERTED.*
      VALUES (@tableId, @userId, GETDATE(), N'Đang phục vụ', @total, @tableKey, @isBuffet)
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id },
      { name: 'userId', type: db.sql.Int, value: user_id || null },
      { name: 'total', type: db.sql.Decimal(18, 2), value: total },
      { name: 'tableKey', type: db.sql.NVarChar(100), value: table_key || null },
      { name: 'isBuffet', type: db.sql.Bit, value: is_buffet || false }
    ]);

    const newOrder = orderResult.recordset[0];

    // Thêm chi tiết đơn hàng
    if (is_buffet) {
      // Đối với buffet, tạo một item đặc biệt
      const detailResult = await db.executeQuery(`
        INSERT INTO order_details (order_id, food_id, quantity, price, custom_name)
        OUTPUT INSERTED.*
        VALUES (@orderId, NULL, 1, @price, @customName)
      `, [
        { name: 'orderId', type: db.sql.Int, value: newOrder.id },
        { name: 'price', type: db.sql.Decimal(10, 2), value: total },
        { name: 'customName', type: db.sql.NVarChar(255), value: 'Buffet - Ăn thoải mái tất cả món ăn' }
      ]);

      // Không thêm vào kitchen queue cho buffet vì không cần chế biến cụ thể
    } else {
      // Đối với đặt món thông thường
      for (const item of items) {
        const foodResult = await db.executeQuery(
          'SELECT price FROM foods WHERE id = @id',
          [{ name: 'id', type: db.sql.Int, value: item.food_id }]
        );

        const detailResult = await db.executeQuery(`
          INSERT INTO order_details (order_id, food_id, quantity, price)
          OUTPUT INSERTED.*
          VALUES (@orderId, @foodId, @quantity, @price)
        `, [
          { name: 'orderId', type: db.sql.Int, value: newOrder.id },
          { name: 'foodId', type: db.sql.Int, value: item.food_id },
          { name: 'quantity', type: db.sql.Int, value: item.quantity },
          { name: 'price', type: db.sql.Decimal(10, 2), value: foodResult.recordset[0].price }
        ]);

        // Thêm vào hàng đợi nhà bếp
        await db.executeQuery(`
          INSERT INTO kitchen_queue (order_detail_id, status, updated_at)
          VALUES (@orderDetailId, 'Chờ chế biến', GETDATE())
        `, [
          { name: 'orderDetailId', type: db.sql.Int, value: detailResult.recordset[0].id }
        ]);
      }
    }

    // Cập nhật trạng thái bàn
    await db.executeQuery(`
      UPDATE tables
      SET status = N'Đang phục vụ'
      WHERE id = @tableId
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id }
    ]);

    // Thông báo qua Socket.IO
    io.to(`table_${table_id}`).emit('new_order', { order_id: newOrder.id });

    // Thông báo đến Kitchen Service
    try {
      await axios.post(`http://localhost:${process.env.KITCHEN_SERVICE_PORT}/api/notify`, {
        order_id: newOrder.id,
        table_id: table_id
      });
    } catch (error) {
      console.error('Error notifying kitchen service:', error);
    }

    // Lấy thông tin đầy đủ của đơn hàng vừa tạo
    const fullOrderResult = await db.executeQuery(`
      SELECT o.*, t.name as table_name, u.username as user_username
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: newOrder.id }]);

    const fullOrder = fullOrderResult.recordset[0];

    // Lấy chi tiết đơn hàng
    const detailsResult = await db.executeQuery(`
      SELECT od.*,
             COALESCE(od.custom_name, f.name) as food_name,
             f.image_url as food_image
      FROM order_details od
      LEFT JOIN foods f ON od.food_id = f.id
      WHERE od.order_id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: newOrder.id }]);

    fullOrder.details = detailsResult.recordset;

    res.status(201).json(fullOrder);
    } catch (error) {
      console.error('Error creating order:', error);
      console.error('Stack trace:', error.stack);
      res.status(500).json({ message: 'Lỗi khi tạo đơn hàng mới' });
    }
});

// Cập nhật trạng thái đơn hàng
app.put('/api/orders/:id/status', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  let pool = null;
  let transaction = null;

  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ message: 'Trạng thái là bắt buộc' });
    }

    // Kiểm tra đơn hàng tồn tại
    const checkResult = await db.executeQuery(
      'SELECT * FROM orders WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    const order = checkResult.recordset[0];

    // Tạo connection pool và transaction
    const config = {
      server: process.env.DB_SERVER,
      database: process.env.DB_DATABASE,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      port: parseInt(process.env.DB_PORT, 10),
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
      }
    };

    pool = await new db.sql.ConnectionPool(config).connect();
    transaction = new db.sql.Transaction(pool);
    await transaction.begin();

    try {
      // Cập nhật trạng thái đơn hàng
      const orderRequest = new db.sql.Request(transaction);
      orderRequest.input('id', db.sql.Int, id);
      orderRequest.input('status', db.sql.NVarChar(20), status);

      const result = await orderRequest.query(`
        UPDATE orders
        SET status = @status
        OUTPUT INSERTED.*
        WHERE id = @id
      `);

      // Nếu đơn hàng hoàn thành và là buffet, reset is_buffet của bàn
      if ((status === 'Đã thanh toán' || status === 'Hoàn thành' || status === 'Đã hoàn thành') && order.is_buffet) {
        const tableRequest = new db.sql.Request(transaction);
        tableRequest.input('tableId', db.sql.Int, order.table_id);

        await tableRequest.query(`
          UPDATE tables
          SET is_buffet = 0, status = N'Trống'
          WHERE id = @tableId
        `);

        console.log(`✅ Reset is_buffet = false cho bàn ${order.table_id} sau khi hoàn thành buffet`);
      }

      // Nếu đơn hàng đã thanh toán, gọi Table Service để reset bàn
      if (status === 'Đã thanh toán') {
        // Commit transaction trước khi gọi API khác
        await transaction.commit();

        // Gọi Table Service API để vô hiệu hóa key và reset trạng thái bàn
        try {
          const tableServiceUrl = `http://localhost:3011/api/tables/${order.table_id}/invalidate-keys`;

          await axios.post(tableServiceUrl, {}, {
            headers: {
              'Authorization': req.headers.authorization
            }
          });
          console.log(`✅ Đã vô hiệu hóa key cho bàn ${order.table_id} sau khi thanh toán`);
        } catch (apiError) {
          console.error('❌ Lỗi khi gọi Table Service:', apiError.message);
          // Không throw error vì đơn hàng đã được cập nhật thành công
        }
      } else {
        // Commit transaction cho các trường hợp khác
        await transaction.commit();
      }



      // Thông báo qua Socket.IO
      io.to(`table_${order.table_id}`).emit('order_status_updated', {
        order_id: id,
        status: status
      });

      res.json(result.recordset[0]);
    } catch (error) {
      // Rollback transaction nếu có lỗi
      if (transaction) {
        await transaction.rollback();
      }
      throw error;
    }
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật trạng thái đơn hàng' });
  } finally {
    // Đóng connection pool
    if (pool) {
      try {
        await pool.close();
      } catch (err) {
        console.error('Error closing connection pool:', err);
      }
    }
  }
});

// Thêm món ăn vào đơn hàng hiện tại
app.post('/api/orders/:id/items', async (req, res) => {
  let pool = null;
  let transaction = null;

  try {
    const { id } = req.params;
    const { items, table_key } = req.body;

    if (!items || items.length === 0) {
      return res.status(400).json({ message: 'Danh sách món ăn là bắt buộc' });
    }

    // Kiểm tra đơn hàng tồn tại
    const orderResult = await db.executeQuery(
      'SELECT * FROM orders WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (orderResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    const order = orderResult.recordset[0];

    // Kiểm tra key của bàn nếu có (bỏ qua việc kiểm tra hết hạn)
    if (table_key) {
      const keyResult = await db.executeQuery(
        'SELECT * FROM table_keys WHERE key_value = @key AND table_id = @tableId AND is_valid = @isValid',
        [
          { name: 'key', type: db.sql.NVarChar(100), value: table_key },
          { name: 'tableId', type: db.sql.Int, value: order.table_id },
          { name: 'isValid', type: db.sql.Bit, value: true }
        ]
      );

      console.log('Kiểm tra key:', table_key, 'Kết quả:', keyResult.recordset);

      if (keyResult.recordset.length === 0) {
        return res.status(403).json({ message: 'Key không hợp lệ' });
      }
    } else if (!req.user) {
      // Nếu không có key và không có user (không xác thực) thì không cho phép
      return res.status(401).json({ message: 'Cần xác thực hoặc cung cấp key hợp lệ' });
    }

    // Tạo connection pool và transaction
    const config = {
      server: process.env.DB_SERVER,
      database: process.env.DB_DATABASE,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      port: parseInt(process.env.DB_PORT, 10),
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
      }
    };

    pool = await new db.sql.ConnectionPool(config).connect();
    transaction = new db.sql.Transaction(pool);
    await transaction.begin();

    try {
      // Tính tổng tiền mới
      let additionalTotal = 0;
      for (const item of items) {
        const foodResult = await db.executeQuery(
          'SELECT price FROM foods WHERE id = @id',
          [{ name: 'id', type: db.sql.Int, value: item.food_id }]
        );

        if (foodResult.recordset.length === 0) {
          await transaction.rollback();
          return res.status(404).json({ message: `Không tìm thấy món ăn với ID ${item.food_id}` });
        }

        additionalTotal += foodResult.recordset[0].price * item.quantity;
      }

      // Cập nhật tổng tiền đơn hàng
      const updateOrderRequest = new db.sql.Request(transaction);
      updateOrderRequest.input('id', db.sql.Int, id);
      updateOrderRequest.input('additionalTotal', db.sql.Decimal(18, 2), additionalTotal);

      await updateOrderRequest.query(`
        UPDATE orders
        SET total = total + @additionalTotal
        WHERE id = @id
      `);

      // Thêm chi tiết đơn hàng mới
      const addedItems = [];
      for (const item of items) {
        const foodResult = await db.executeQuery(
          'SELECT price FROM foods WHERE id = @id',
          [{ name: 'id', type: db.sql.Int, value: item.food_id }]
        );

        const detailRequest = new db.sql.Request(transaction);
        detailRequest.input('orderId', db.sql.Int, id);
        detailRequest.input('foodId', db.sql.Int, item.food_id);
        detailRequest.input('quantity', db.sql.Int, item.quantity);
        detailRequest.input('price', db.sql.Decimal(10, 2), foodResult.recordset[0].price);

        const detailResult = await detailRequest.query(`
          INSERT INTO order_details (order_id, food_id, quantity, price)
          OUTPUT INSERTED.*
          VALUES (@orderId, @foodId, @quantity, @price)
        `);

        const newDetail = detailResult.recordset[0];
        addedItems.push(newDetail);

        // Thêm vào hàng đợi nhà bếp
        const kitchenRequest = new db.sql.Request(transaction);
        kitchenRequest.input('orderDetailId', db.sql.Int, newDetail.id);

        await kitchenRequest.query(`
          INSERT INTO kitchen_queue (order_detail_id, status, updated_at)
          VALUES (@orderDetailId, 'Chờ chế biến', GETDATE())
        `);
      }

      // Commit transaction
      await transaction.commit();

      // Thông báo qua Socket.IO
      io.to(`table_${order.table_id}`).emit('order_updated', {
        order_id: id,
        added_items: addedItems
      });

      // Thông báo đến Kitchen Service
      try {
        await axios.post(`http://localhost:${process.env.KITCHEN_SERVICE_PORT}/api/notify`, {
          order_id: id,
          table_id: order.table_id,
          added_items: addedItems
        });
      } catch (error) {
        console.error('Error notifying kitchen service:', error);
      }

      // Lấy thông tin đầy đủ của đơn hàng vừa cập nhật
      const fullOrderResult = await db.executeQuery(`
        SELECT o.*, t.name as table_name, u.username as user_username
        FROM orders o
        LEFT JOIN tables t ON o.table_id = t.id
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.id = @id
      `, [{ name: 'id', type: db.sql.Int, value: id }]);

      const fullOrder = fullOrderResult.recordset[0];

      // Lấy chi tiết đơn hàng
      const detailsResult = await db.executeQuery(`
        SELECT od.*, f.name as food_name, f.image_url as food_image
        FROM order_details od
        JOIN foods f ON od.food_id = f.id
        WHERE od.order_id = @orderId
      `, [{ name: 'orderId', type: db.sql.Int, value: id }]);

      fullOrder.details = detailsResult.recordset;

      res.json(fullOrder);
    } catch (error) {
      // Rollback transaction nếu có lỗi
      if (transaction) {
        await transaction.rollback();
      }
      throw error;
    }
  } catch (error) {
    console.error('Error adding items to order:', error);
    res.status(500).json({ message: 'Lỗi khi thêm món ăn vào đơn hàng' });
  } finally {
    // Đóng connection pool
    if (pool) {
      try {
        await pool.close();
      } catch (err) {
        console.error('Error closing connection pool:', err);
      }
    }
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Khởi động server
server.listen(PORT, () => {
  console.log(`Order Service đang chạy tại http://localhost:${PORT}`);
});
