/* Reset CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    /* Aurora Color Palette */
    --aurora-primary: #667eea;
    --aurora-secondary: #764ba2;
    --aurora-accent: #f093fb;
    --aurora-light: #c3cfe2;
    --aurora-dark: #2c3e50;
    
    /* Functional Colors */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    /* UI Colors */
    --text-color: #334155;
    --text-light: #64748b;
    --white: #ffffff;
    
    /* Layout */
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-sm: 8px;
    
    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
    --shadow-aurora: 0 8px 32px rgba(102, 126, 234, 0.2);
    
    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s ease;
}

/* Body and Background */
body {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--aurora-primary) 0%, var(--aurora-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow: hidden;
    position: relative;
}

/* Aurora Background Animation */
.aurora-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.aurora-light {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.6;
    animation: auroraFloat 8s ease-in-out infinite;
}

.aurora-light-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.aurora-light-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(118, 75, 162, 0.3) 0%, transparent 70%);
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.aurora-light-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(240, 147, 251, 0.4) 0%, transparent 70%);
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes auroraFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.6;
    }
    33% {
        transform: translate(30px, -30px) scale(1.1);
        opacity: 0.8;
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
        opacity: 0.4;
    }
}

/* Login Container */
.login-container {
    width: 100%;
    max-width: 450px;
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Card */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-secondary));
}

/* Login Header */
.login-header {
    padding: 40px 40px 20px;
    text-align: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    font-size: 48px;
    margin-bottom: 8px;
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.logo-text {
    font-size: 36px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: 1px;
}

.logo-subtitle {
    font-size: 14px;
    color: var(--text-light);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

/* Form Container */
.login-form-container {
    padding: 20px 40px 40px;
}

.form-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.form-title i {
    color: var(--aurora-primary);
}

.form-subtitle {
    color: var(--text-light);
    margin-bottom: 32px;
    font-size: 15px;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label i {
    color: var(--aurora-primary);
    width: 16px;
}

.form-group input {
    padding: 16px 20px;
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: var(--border-radius);
    font-size: 15px;
    transition: var(--transition);
    background: white;
    color: var(--text-color);
    height: 56px;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--aurora-primary);
    box-shadow: var(--shadow-aurora);
    transform: translateY(-2px);
}

.form-group input::placeholder {
    color: var(--text-light);
}

/* Password Input Container */
.password-input-container {
    position: relative;
    height: 56px;
}

.password-input-container input {
    width: 100%;
    height: 100%;
    padding-right: 50px;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 4px;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--aurora-primary);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-color);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--aurora-primary);
    border-color: var(--aurora-primary);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Login Button */
.login-btn {
    background: linear-gradient(135deg, var(--aurora-primary), var(--aurora-secondary));
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-aurora);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Error Message */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    padding: 12px 16px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(239, 68, 68, 0.2);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    margin-top: 16px;
}

/* Login Footer */
.login-footer {
    padding: 20px 40px;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.login-footer p {
    color: var(--text-light);
    font-size: 13px;
    margin: 0;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(400px);
    transition: var(--transition);
    z-index: 1000;
    border-left: 4px solid var(--aurora-primary);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification-icon {
    font-size: 18px;
}

.notification.success .notification-icon::before {
    content: '✓';
    color: var(--success-color);
}

.notification.error .notification-icon::before {
    content: '✕';
    color: var(--danger-color);
}

.notification-message {
    font-weight: 500;
    color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        max-width: 100%;
        margin: 0 10px;
    }
    
    .login-header,
    .login-form-container,
    .login-footer {
        padding-left: 24px;
        padding-right: 24px;
    }
    
    .logo-text {
        font-size: 28px;
    }
    
    .form-title {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .login-header,
    .login-form-container,
    .login-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .logo-icon {
        font-size: 36px;
    }
    
    .logo-text {
        font-size: 24px;
    }
    
    .form-title {
        font-size: 18px;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}
