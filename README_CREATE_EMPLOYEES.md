# Script Tạo 30 Tài Khoản Nhân Viên

Script này sẽ tự động tạo 30 tài khoản nhân viên cho hệ thống quản lý nhà hàng.

## Thông tin tài khoản được tạo

### Cấu hình chung:
- **<PERSON><PERSON> trò**: <PERSON><PERSON><PERSON> viên (role_id = 2)
- **<PERSON><PERSON><PERSON> khẩu mặc định**: `123456` (cho tất c<PERSON> tà<PERSON> kho<PERSON>)
- **Username**: `nhanvien01` đến `nhanvien30`

### Danh sách 30 nhân viên:

1. **nhanvien01** - <PERSON><PERSON><PERSON><PERSON> (25 tuổi)
2. **nhanvien02** - Tr<PERSON><PERSON> (28 tuổi)
3. **nhanvien03** - <PERSON><PERSON> <PERSON><PERSON> (30 tuổi)
4. **nhanvien04** - <PERSON><PERSON><PERSON><PERSON> (26 tuổi)
5. **nhanvien05** - <PERSON><PERSON><PERSON> (24 tuổi)
6. **nhanvien06** - <PERSON><PERSON><PERSON> (29 tuổi)
7. **nhanvien07** - Đặng <PERSON>iang (27 tuổi)
8. **nhanvien08** - Bùi Thị Hoa (31 tuổi)
9. **nhanvien09** - <PERSON><PERSON> V<PERSON>n Inh (23 tuổi)
10. **nhanvien10** - Lý Thị <PERSON> (32 tuổi)
11. **nhanvien11** - Tr<PERSON>ơng V<PERSON>n Long (28 tuổi)
12. **nhanvien12** - Đinh Thị Mai (25 tuổi)
13. **nhanvien13** - Phan Văn Nam (30 tuổi)
14. **nhanvien14** - Cao Thị Oanh (26 tuổi)
15. **nhanvien15** - Dương Văn Phúc (29 tuổi)
16. **nhanvien16** - Võ Thị Quỳnh (27 tuổi)
17. **nhanvien17** - Lâm Văn Rồng (33 tuổi)
18. **nhanvien18** - Huỳnh Thị Sương (24 tuổi)
19. **nhanvien19** - Tô Văn Tài (31 tuổi)
20. **nhanvien20** - Đỗ Thị Uyên (26 tuổi)
21. **nhanvien21** - Mạc Văn Vinh (28 tuổi)
22. **nhanvien22** - Lưu Thị Xuân (25 tuổi)
23. **nhanvien23** - Kiều Văn Yên (30 tuổi)
24. **nhanvien24** - Ông Thị Zung (29 tuổi)
25. **nhanvien25** - Nguyễn Văn Anh (27 tuổi)
26. **nhanvien26** - Trần Thị Bảo (32 tuổi)
27. **nhanvien27** - Lê Văn Cảnh (26 tuổi)
28. **nhanvien28** - Phạm Thị Diệu (28 tuổi)
29. **nhanvien29** - Hoàng Văn Ế (24 tuổi)
30. **nhanvien30** - Vũ Thị Phượng (31 tuổi)

## Yêu cầu trước khi chạy

1. **Hệ thống đang chạy**: Đảm bảo tất cả các service đang hoạt động
   - User Service: `http://localhost:3001`
   - API Gateway: `http://localhost:3000`

2. **Tài khoản admin**: Cần có tài khoản admin để tạo nhân viên
   - Username: `admin`
   - Password: `admin123`

3. **Dependencies**: Cài đặt axios nếu chưa có
   ```bash
   npm install axios
   ```

## Cách chạy script

### Phương pháp 1: Chạy trực tiếp
```bash
node create_employees.js
```

### Phương pháp 2: Import vào script khác
```javascript
const { createAllEmployees } = require('./create_employees.js');

// Chạy function
createAllEmployees();
```

## Kết quả mong đợi

Script sẽ hiển thị:
- ✅ Thông báo đăng nhập admin thành công
- ✅ Thông báo tạo từng nhân viên thành công
- ❌ Thông báo lỗi nếu có (ví dụ: username đã tồn tại)
- 📊 Báo cáo tổng kết cuối cùng

## Xử lý lỗi

### Lỗi thường gặp:
1. **Connection refused**: Service chưa chạy
2. **Username already exists**: Tài khoản đã tồn tại
3. **Invalid credentials**: Sai thông tin admin

### Giải pháp:
- Kiểm tra tất cả service đang chạy
- Xóa tài khoản trùng lặp nếu cần
- Kiểm tra thông tin đăng nhập admin

## Lưu ý bảo mật

- Mật khẩu mặc định `123456` chỉ dùng cho môi trường test
- Nên thay đổi mật khẩu sau khi tạo tài khoản
- Không commit file này vào production với thông tin thật

## Tùy chỉnh

Để thay đổi thông tin nhân viên, chỉnh sửa mảng `employees` trong file `create_employees.js`:

```javascript
{
  username: 'nhanvien01',
  password: '123456',
  role_id: 2,
  full_name: 'Tên đầy đủ',
  phone_number: '0901234567',
  age: 25,
  email: '<EMAIL>',
  address: 'Địa chỉ đầy đủ'
}
```
